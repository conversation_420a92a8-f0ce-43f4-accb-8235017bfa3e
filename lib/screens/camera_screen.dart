import 'package:flutter/material.dart';

class CameraScreen extends StatefulWidget {
  final dynamic meterType;
  
  const CameraScreen({super.key, required this.meterType});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Camera - ${widget.meterType.displayName}'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.camera_alt, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('Camera functionality will be implemented here'),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/reading_provider.dart';
import '../services/report_service.dart';
import '../services/share_service.dart';
import '../models/meter_type.dart';

// Chart data model
class ChartData {
  final String x;
  final double y;

  ChartData(this.x, this.y);
}

class ReportAnalysisScreen extends ConsumerStatefulWidget {
  const ReportAnalysisScreen({super.key});

  @override
  ConsumerState<ReportAnalysisScreen> createState() =>
      _ReportAnalysisScreenState();
}

class _ReportAnalysisScreenState extends ConsumerState<ReportAnalysisScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ReportService _reportService = ReportService();
  final ShareService _shareService = ShareService();

  int _selectedYear = DateTime.now().year;
  String _selectedPeriod = 'monthly';
  bool _isLoading = false;

  Map<String, dynamic>? _monthlyReport;
  Map<String, dynamic>? _yearlyReport;
  Map<String, dynamic>? _buildingReport;
  Map<String, dynamic>? _trendsReport;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _generateReports();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Report Analysis'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateReports,
            tooltip: 'Refresh Reports',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share),
                        SizedBox(width: 8),
                        Text('Share Report'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'export',
                    child: Row(
                      children: [
                        Icon(Icons.download),
                        SizedBox(width: 8),
                        Text('Export Data'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.calendar_month), text: 'Monthly'),
            Tab(icon: Icon(Icons.calendar_today), text: 'Yearly'),
            Tab(icon: Icon(Icons.business), text: 'Buildings'),
            Tab(icon: Icon(Icons.trending_up), text: 'Trends'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Period selector
          _buildPeriodSelector(),

          // Content
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildMonthlyReportTab(),
                        _buildYearlyReportTab(),
                        _buildBuildingReportTab(),
                        _buildTrendsReportTab(),
                      ],
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          Expanded(
            child: DropdownButtonFormField<int>(
              value: _selectedYear,
              decoration: const InputDecoration(
                labelText: 'Year',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items:
                  _getAvailableYears().map((year) {
                    return DropdownMenuItem(
                      value: year,
                      child: Text(year.toString()),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedYear = value;
                  });
                  _generateReports();
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedPeriod,
              decoration: const InputDecoration(
                labelText: 'Period',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items: const [
                DropdownMenuItem(value: 'monthly', child: Text('Monthly View')),
                DropdownMenuItem(
                  value: 'quarterly',
                  child: Text('Quarterly View'),
                ),
                DropdownMenuItem(value: 'yearly', child: Text('Yearly View')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedPeriod = value;
                  });
                  _generateReports();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyReportTab() {
    if (_monthlyReport == null) {
      return const Center(child: Text('No data available'));
    }

    final monthlyData =
        _monthlyReport!['monthlyData'] as Map<int, Map<String, dynamic>>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary cards
          _buildSummaryCards(_monthlyReport!),
          const SizedBox(height: 24),

          // Monthly consumption chart
          _buildMonthlyConsumptionChart(monthlyData),
          const SizedBox(height: 24),

          // Monthly statistics table
          _buildMonthlyStatisticsTable(monthlyData),
          const SizedBox(height: 24),

          // Peak consumption analysis
          _buildPeakAnalysis(_monthlyReport!),
        ],
      ),
    );
  }

  Widget _buildYearlyReportTab() {
    if (_yearlyReport == null) {
      return const Center(child: Text('No data available'));
    }

    final yearlyData =
        _yearlyReport!['yearlyData'] as Map<int, Map<String, dynamic>>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Yearly overview cards
          _buildYearlyOverviewCards(_yearlyReport!),
          const SizedBox(height: 24),

          // Year-over-year comparison chart
          _buildYearlyComparisonChart(yearlyData),
          const SizedBox(height: 24),

          // Growth rate analysis
          _buildGrowthRateAnalysis(yearlyData),
          const SizedBox(height: 24),

          // Yearly statistics table
          _buildYearlyStatisticsTable(yearlyData),
        ],
      ),
    );
  }

  Widget _buildBuildingReportTab() {
    if (_buildingReport == null) {
      return const Center(child: Text('No data available'));
    }

    final buildingData =
        _buildingReport!['buildingData'] as Map<String, Map<String, dynamic>>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Building overview cards
          _buildBuildingOverviewCards(_buildingReport!),
          const SizedBox(height: 24),

          // Building consumption comparison chart
          _buildBuildingComparisonChart(buildingData),
          const SizedBox(height: 24),

          // Building efficiency analysis
          _buildBuildingEfficiencyAnalysis(buildingData),
          const SizedBox(height: 24),

          // Building statistics table
          _buildBuildingStatisticsTable(buildingData),
        ],
      ),
    );
  }

  Widget _buildTrendsReportTab() {
    if (_trendsReport == null) {
      return const Center(child: Text('No data available'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trend overview cards
          _buildTrendOverviewCards(_trendsReport!),
          const SizedBox(height: 24),

          // Consumption trends chart
          _buildConsumptionTrendsChart(_trendsReport!),
          const SizedBox(height: 24),

          // Seasonal patterns analysis
          _buildSeasonalPatternsChart(_trendsReport!),
          const SizedBox(height: 24),

          // Predictions and forecasts
          _buildPredictionsAnalysis(_trendsReport!),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(Map<String, dynamic> report) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Total Readings',
            '${report['totalReadings']}',
            Icons.assessment,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Buildings',
            '${report['totalBuildings']}',
            Icons.business,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Rooms',
            '${report['totalRooms']}',
            Icons.room,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Avg/Month',
            '${report['averageMonthlyReadings'].toStringAsFixed(1)}',
            Icons.trending_up,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlyConsumptionChart(
    Map<int, Map<String, dynamic>> monthlyData,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Monthly Consumption Trends',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          const months = [
                            'Jan',
                            'Feb',
                            'Mar',
                            'Apr',
                            'May',
                            'Jun',
                            'Jul',
                            'Aug',
                            'Sep',
                            'Oct',
                            'Nov',
                            'Dec',
                          ];
                          if (value.toInt() >= 0 &&
                              value.toInt() < months.length) {
                            return Text(
                              months[value.toInt()],
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: _buildMonthlyConsumptionLines(monthlyData),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<LineChartBarData> _buildMonthlyConsumptionLines(
    Map<int, Map<String, dynamic>> monthlyData,
  ) {
    final lines = <LineChartBarData>[];

    for (final meterType in MeterType.values) {
      final spots = <FlSpot>[];

      for (int month = 1; month <= 12; month++) {
        final consumption =
            monthlyData[month]!['totalConsumption'][meterType] ?? 0.0;
        spots.add(FlSpot((month - 1).toDouble(), consumption));
      }

      lines.add(
        LineChartBarData(
          spots: spots,
          color: _getMeterTypeColor(meterType),
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: FlDotData(show: true),
          belowBarData: BarAreaData(show: false),
        ),
      );
    }

    return lines;
  }

  Widget _buildMonthlyStatisticsTable(
    Map<int, Map<String, dynamic>> monthlyData,
  ) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Monthly Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('Month')),
                  DataColumn(label: Text('Readings')),
                  DataColumn(label: Text('Buildings')),
                  DataColumn(label: Text('Rooms')),
                  DataColumn(label: Text('Total Consumption')),
                ],
                rows: List.generate(12, (index) {
                  final month = index + 1;
                  final data = monthlyData[month]!;
                  final totalConsumption = (data['totalConsumption']
                          as Map<MeterType, double>)
                      .values
                      .fold<double>(0, (sum, value) => sum + value);

                  return DataRow(
                    cells: [
                      DataCell(Text(monthNames[index])),
                      DataCell(Text('${data['totalReadings']}')),
                      DataCell(Text('${(data['buildings'] as Set).length}')),
                      DataCell(Text('${(data['rooms'] as Set).length}')),
                      DataCell(Text(totalConsumption.toStringAsFixed(2))),
                    ],
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeakAnalysis(Map<String, dynamic> report) {
    final peakMonth = report['peakMonth'] as Map<String, dynamic>;
    const monthNames = [
      '',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Peak Consumption Analysis',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildAnalysisCard(
                    'Peak Month',
                    monthNames[peakMonth['month']],
                    Icons.calendar_month,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildAnalysisCard(
                    'Peak Meter Type',
                    (peakMonth['meterType'] as MeterType).displayName,
                    Icons.electrical_services,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildAnalysisCard(
                    'Peak Consumption',
                    (peakMonth['consumption'] as double).toStringAsFixed(2),
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods and missing implementations

  List<int> _getAvailableYears() {
    final readingState = ref.read(readingProvider);
    final years =
        readingState.readings.map((r) => r.timestamp.year).toSet().toList()
          ..sort();
    if (years.isEmpty) {
      years.add(DateTime.now().year);
    }
    return years;
  }

  Future<void> _generateReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final readingState = ref.read(readingProvider);
      final readings = readingState.readings;

      // Generate all reports
      _monthlyReport = _reportService.generateMonthlyReport(
        readings,
        _selectedYear,
      );
      _yearlyReport = _reportService.generateYearlyReport(readings);
      _buildingReport = _reportService.generateBuildingReport(readings);
      _trendsReport = _reportService.generateConsumptionTrends(readings);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate reports: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _shareReport();
        break;
      case 'export':
        _exportReport();
        break;
    }
  }

  Future<void> _shareReport() async {
    try {
      // Generate report summary text
      final reportText = _generateReportSummaryText();
      await _shareService.shareReadingsAsText([
        {'report': reportText},
      ], title: 'Meter Reading Analysis Report');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Report shared successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share report: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality will be implemented'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  String _generateReportSummaryText() {
    final buffer = StringBuffer();
    buffer.writeln('📊 Meter Reading Analysis Report');
    buffer.writeln('');
    buffer.writeln('📅 Period: $_selectedPeriod analysis for $_selectedYear');
    buffer.writeln('📈 Generated: ${DateTime.now().toString().split('.')[0]}');
    buffer.writeln('');

    if (_monthlyReport != null) {
      buffer.writeln('📋 Monthly Summary:');
      buffer.writeln('• Total Readings: ${_monthlyReport!['totalReadings']}');
      buffer.writeln('• Buildings: ${_monthlyReport!['totalBuildings']}');
      buffer.writeln('• Rooms: ${_monthlyReport!['totalRooms']}');
      buffer.writeln('');
    }

    return buffer.toString();
  }

  Color _getMeterTypeColor(MeterType meterType) {
    switch (meterType) {
      case MeterType.main:
        return Colors.blue;
      case MeterType.generator:
        return Colors.orange;
      case MeterType.utility:
        return Colors.purple;
      case MeterType.water:
        return Colors.cyan;
    }
  }

  // Placeholder methods for missing chart builders
  Widget _buildYearlyOverviewCards(Map<String, dynamic> report) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          'Yearly Overview - ${report['availableYears']?.length ?? 0} years of data',
        ),
      ),
    );
  }

  Widget _buildYearlyComparisonChart(
    Map<int, Map<String, dynamic>> yearlyData,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Yearly Comparison Chart - ${yearlyData.length} years'),
      ),
    );
  }

  Widget _buildGrowthRateAnalysis(Map<int, Map<String, dynamic>> yearlyData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: const Text('Growth Rate Analysis'),
      ),
    );
  }

  Widget _buildYearlyStatisticsTable(
    Map<int, Map<String, dynamic>> yearlyData,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: const Text('Yearly Statistics Table'),
      ),
    );
  }

  Widget _buildBuildingOverviewCards(Map<String, dynamic> report) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          'Building Overview - ${report['totalBuildings']} buildings',
        ),
      ),
    );
  }

  Widget _buildBuildingComparisonChart(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Building Comparison - ${buildingData.length} buildings'),
      ),
    );
  }

  Widget _buildBuildingEfficiencyAnalysis(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: const Text('Building Efficiency Analysis'),
      ),
    );
  }

  Widget _buildBuildingStatisticsTable(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: const Text('Building Statistics Table'),
      ),
    );
  }

  Widget _buildTrendOverviewCards(Map<String, dynamic> report) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: const Text('Trend Overview'),
      ),
    );
  }

  Widget _buildConsumptionTrendsChart(Map<String, dynamic> report) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: const Text('Consumption Trends Chart'),
      ),
    );
  }

  Widget _buildSeasonalPatternsChart(Map<String, dynamic> report) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: const Text('Seasonal Patterns Chart'),
      ),
    );
  }

  Widget _buildPredictionsAnalysis(Map<String, dynamic> report) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: const Text('Predictions Analysis'),
      ),
    );
  }
}

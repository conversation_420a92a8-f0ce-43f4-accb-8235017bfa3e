import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/reading_provider.dart';
import '../services/report_service.dart';
import '../services/share_service.dart';
import '../models/meter_type.dart';

// Chart data model
class ChartData {
  final String x;
  final double y;

  ChartData(this.x, this.y);
}

class ReportAnalysisScreen extends ConsumerStatefulWidget {
  const ReportAnalysisScreen({super.key});

  @override
  ConsumerState<ReportAnalysisScreen> createState() =>
      _ReportAnalysisScreenState();
}

class _ReportAnalysisScreenState extends ConsumerState<ReportAnalysisScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ReportService _reportService = ReportService();
  final ShareService _shareService = ShareService();

  int _selectedYear = DateTime.now().year;
  String _selectedPeriod = 'monthly';
  bool _isLoading = false;

  Map<String, dynamic>? _monthlyReport;
  Map<String, dynamic>? _yearlyReport;
  Map<String, dynamic>? _buildingReport;
  Map<String, dynamic>? _trendsReport;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _generateReports();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Report Analysis'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateReports,
            tooltip: 'Refresh Reports',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share),
                        SizedBox(width: 8),
                        Text('Share Report'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'export',
                    child: Row(
                      children: [
                        Icon(Icons.download),
                        SizedBox(width: 8),
                        Text('Export Data'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.calendar_month), text: 'Monthly'),
            Tab(icon: Icon(Icons.calendar_today), text: 'Yearly'),
            Tab(icon: Icon(Icons.business), text: 'Buildings'),
            Tab(icon: Icon(Icons.trending_up), text: 'Trends'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Period selector
          _buildPeriodSelector(),

          // Content
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildMonthlyReportTab(),
                        _buildYearlyReportTab(),
                        _buildBuildingReportTab(),
                        _buildTrendsReportTab(),
                      ],
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          Expanded(
            child: DropdownButtonFormField<int>(
              value: _selectedYear,
              decoration: const InputDecoration(
                labelText: 'Year',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items:
                  _getAvailableYears().map((year) {
                    return DropdownMenuItem(
                      value: year,
                      child: Text(year.toString()),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedYear = value;
                  });
                  _generateReports();
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedPeriod,
              decoration: const InputDecoration(
                labelText: 'Period',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items: const [
                DropdownMenuItem(value: 'monthly', child: Text('Monthly View')),
                DropdownMenuItem(
                  value: 'quarterly',
                  child: Text('Quarterly View'),
                ),
                DropdownMenuItem(value: 'yearly', child: Text('Yearly View')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedPeriod = value;
                  });
                  _generateReports();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyReportTab() {
    if (_monthlyReport == null) {
      return const Center(child: Text('No data available'));
    }

    final monthlyData =
        _monthlyReport!['monthlyData'] as Map<int, Map<String, dynamic>>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary cards
          _buildSummaryCards(_monthlyReport!),
          const SizedBox(height: 24),

          // Monthly consumption chart
          _buildMonthlyConsumptionChart(monthlyData),
          const SizedBox(height: 24),

          // Monthly statistics table
          _buildMonthlyStatisticsTable(monthlyData),
          const SizedBox(height: 24),

          // Peak consumption analysis
          _buildPeakAnalysis(_monthlyReport!),
        ],
      ),
    );
  }

  Widget _buildYearlyReportTab() {
    if (_yearlyReport == null) {
      return const Center(child: Text('No data available'));
    }

    final yearlyData =
        _yearlyReport!['yearlyData'] as Map<int, Map<String, dynamic>>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Yearly overview cards
          _buildYearlyOverviewCards(_yearlyReport!),
          const SizedBox(height: 24),

          // Year-over-year comparison chart
          _buildYearlyComparisonChart(yearlyData),
          const SizedBox(height: 24),

          // Growth rate analysis
          _buildGrowthRateAnalysis(yearlyData),
          const SizedBox(height: 24),

          // Yearly statistics table
          _buildYearlyStatisticsTable(yearlyData),
        ],
      ),
    );
  }

  Widget _buildBuildingReportTab() {
    if (_buildingReport == null) {
      return const Center(child: Text('No data available'));
    }

    final buildingData =
        _buildingReport!['buildingData'] as Map<String, Map<String, dynamic>>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Building overview cards
          _buildBuildingOverviewCards(_buildingReport!),
          const SizedBox(height: 24),

          // Building consumption comparison chart
          _buildBuildingComparisonChart(buildingData),
          const SizedBox(height: 24),

          // Building efficiency analysis
          _buildBuildingEfficiencyAnalysis(buildingData),
          const SizedBox(height: 24),

          // Building statistics table
          _buildBuildingStatisticsTable(buildingData),
        ],
      ),
    );
  }

  Widget _buildTrendsReportTab() {
    if (_trendsReport == null) {
      return const Center(child: Text('No data available'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trend overview cards
          _buildTrendOverviewCards(_trendsReport!),
          const SizedBox(height: 24),

          // Consumption trends chart
          _buildConsumptionTrendsChart(_trendsReport!),
          const SizedBox(height: 24),

          // Seasonal patterns analysis
          _buildSeasonalPatternsChart(_trendsReport!),
          const SizedBox(height: 24),

          // Predictions and forecasts
          _buildPredictionsAnalysis(_trendsReport!),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(Map<String, dynamic> report) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Total Readings',
            '${report['totalReadings']}',
            Icons.assessment,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Buildings',
            '${report['totalBuildings']}',
            Icons.business,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Rooms',
            '${report['totalRooms']}',
            Icons.room,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Avg/Month',
            '${report['averageMonthlyReadings'].toStringAsFixed(1)}',
            Icons.trending_up,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlyConsumptionChart(
    Map<int, Map<String, dynamic>> monthlyData,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Monthly Consumption Trends',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          const months = [
                            'Jan',
                            'Feb',
                            'Mar',
                            'Apr',
                            'May',
                            'Jun',
                            'Jul',
                            'Aug',
                            'Sep',
                            'Oct',
                            'Nov',
                            'Dec',
                          ];
                          if (value.toInt() >= 0 &&
                              value.toInt() < months.length) {
                            return Text(
                              months[value.toInt()],
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: _buildMonthlyConsumptionLines(monthlyData),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<LineChartBarData> _buildMonthlyConsumptionLines(
    Map<int, Map<String, dynamic>> monthlyData,
  ) {
    final lines = <LineChartBarData>[];

    for (final meterType in MeterType.values) {
      final spots = <FlSpot>[];

      for (int month = 1; month <= 12; month++) {
        final consumption =
            monthlyData[month]!['totalConsumption'][meterType] ?? 0.0;
        spots.add(FlSpot((month - 1).toDouble(), consumption));
      }

      lines.add(
        LineChartBarData(
          spots: spots,
          color: _getMeterTypeColor(meterType),
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: FlDotData(show: true),
          belowBarData: BarAreaData(show: false),
        ),
      );
    }

    return lines;
  }

  Widget _buildMonthlyStatisticsTable(
    Map<int, Map<String, dynamic>> monthlyData,
  ) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Monthly Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('Month')),
                  DataColumn(label: Text('Readings')),
                  DataColumn(label: Text('Buildings')),
                  DataColumn(label: Text('Rooms')),
                  DataColumn(label: Text('Total Consumption')),
                ],
                rows: List.generate(12, (index) {
                  final month = index + 1;
                  final data = monthlyData[month]!;
                  final totalConsumption = (data['totalConsumption']
                          as Map<MeterType, double>)
                      .values
                      .fold<double>(0, (sum, value) => sum + value);

                  return DataRow(
                    cells: [
                      DataCell(Text(monthNames[index])),
                      DataCell(Text('${data['totalReadings']}')),
                      DataCell(Text('${(data['buildings'] as Set).length}')),
                      DataCell(Text('${(data['rooms'] as Set).length}')),
                      DataCell(Text(totalConsumption.toStringAsFixed(2))),
                    ],
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeakAnalysis(Map<String, dynamic> report) {
    final peakMonth = report['peakMonth'] as Map<String, dynamic>;
    const monthNames = [
      '',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Peak Consumption Analysis',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildAnalysisCard(
                    'Peak Month',
                    monthNames[peakMonth['month']],
                    Icons.calendar_month,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildAnalysisCard(
                    'Peak Meter Type',
                    (peakMonth['meterType'] as MeterType).displayName,
                    Icons.electrical_services,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildAnalysisCard(
                    'Peak Consumption',
                    (peakMonth['consumption'] as double).toStringAsFixed(2),
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods and missing implementations

  List<int> _getAvailableYears() {
    final readingState = ref.read(readingProvider);
    final years =
        readingState.readings.map((r) => r.timestamp.year).toSet().toList()
          ..sort();
    if (years.isEmpty) {
      years.add(DateTime.now().year);
    }
    return years;
  }

  Future<void> _generateReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final readingState = ref.read(readingProvider);
      final readings = readingState.readings;

      // Generate all reports
      _monthlyReport = _reportService.generateMonthlyReport(
        readings,
        _selectedYear,
      );
      _yearlyReport = _reportService.generateYearlyReport(readings);
      _buildingReport = _reportService.generateBuildingReport(readings);
      _trendsReport = _reportService.generateConsumptionTrends(readings);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate reports: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _shareReport();
        break;
      case 'export':
        _exportReport();
        break;
    }
  }

  Future<void> _shareReport() async {
    try {
      // Generate report summary text
      final reportText = _generateReportSummaryText();
      await _shareService.shareReadingsAsText([
        {'report': reportText},
      ], title: 'Meter Reading Analysis Report');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Report shared successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share report: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality will be implemented'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  String _generateReportSummaryText() {
    final buffer = StringBuffer();
    buffer.writeln('📊 Meter Reading Analysis Report');
    buffer.writeln('');
    buffer.writeln('📅 Period: $_selectedPeriod analysis for $_selectedYear');
    buffer.writeln('📈 Generated: ${DateTime.now().toString().split('.')[0]}');
    buffer.writeln('');

    if (_monthlyReport != null) {
      buffer.writeln('📋 Monthly Summary:');
      buffer.writeln('• Total Readings: ${_monthlyReport!['totalReadings']}');
      buffer.writeln('• Buildings: ${_monthlyReport!['totalBuildings']}');
      buffer.writeln('• Rooms: ${_monthlyReport!['totalRooms']}');
      buffer.writeln('');
    }

    return buffer.toString();
  }

  Color _getMeterTypeColor(MeterType meterType) {
    switch (meterType) {
      case MeterType.main:
        return Colors.blue;
      case MeterType.generator:
        return Colors.orange;
      case MeterType.utility:
        return Colors.purple;
      case MeterType.water:
        return Colors.cyan;
    }
  }

  // Placeholder methods for missing chart builders
  Widget _buildYearlyOverviewCards(Map<String, dynamic> report) {
    final availableYears = report['availableYears'] as List<int>;
    final overallTrends = report['overallTrends'] as Map<String, dynamic>;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Years of Data',
            '${availableYears.length}',
            Icons.calendar_today,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Total Readings',
            '${report['totalReadings']}',
            Icons.assessment,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Overall Trend',
            _getTrendDisplayText(overallTrends['trend'] ?? 'stable'),
            Icons.trending_up,
            _getTrendColor(overallTrends['trend'] ?? 'stable'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Avg Growth',
            '${(overallTrends['averageGrowthRate'] ?? 0.0).toStringAsFixed(1)}%',
            Icons.show_chart,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildYearlyComparisonChart(
    Map<int, Map<String, dynamic>> yearlyData,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Year-over-Year Consumption Comparison',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: _getMaxYearlyConsumption(yearlyData) * 1.2,
                  barTouchData: BarTouchData(enabled: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final years = yearlyData.keys.toList()..sort();
                          if (value.toInt() >= 0 &&
                              value.toInt() < years.length) {
                            return Text(years[value.toInt()].toString());
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  barGroups: _buildYearlyBarGroups(yearlyData),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGrowthRateAnalysis(Map<int, Map<String, dynamic>> yearlyData) {
    final years = yearlyData.keys.toList()..sort();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Growth Rate Analysis',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (years.length >= 2) ...[
              SizedBox(
                height: 250,
                child: LineChart(
                  LineChartData(
                    gridData: FlGridData(show: true),
                    titlesData: FlTitlesData(
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            return Text('${value.toStringAsFixed(0)}%');
                          },
                        ),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            if (value.toInt() >= 0 &&
                                value.toInt() < years.length) {
                              return Text(years[value.toInt()].toString());
                            }
                            return const Text('');
                          },
                        ),
                      ),
                      topTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      rightTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                    ),
                    borderData: FlBorderData(show: true),
                    lineBarsData: [
                      LineChartBarData(
                        spots: _buildGrowthRateSpots(yearlyData),
                        color: Colors.orange,
                        barWidth: 3,
                        isStrokeCapRound: true,
                        dotData: FlDotData(show: true),
                        belowBarData: BarAreaData(
                          show: true,
                          color: Colors.orange.withValues(alpha: 0.3),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              Container(
                height: 100,
                alignment: Alignment.center,
                child: const Text(
                  'Need at least 2 years of data for growth rate analysis',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildYearlyStatisticsTable(
    Map<int, Map<String, dynamic>> yearlyData,
  ) {
    final years = yearlyData.keys.toList()..sort();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Yearly Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('Year')),
                  DataColumn(label: Text('Readings')),
                  DataColumn(label: Text('Buildings')),
                  DataColumn(label: Text('Rooms')),
                  DataColumn(label: Text('Total Consumption')),
                  DataColumn(label: Text('Growth Rate')),
                  DataColumn(label: Text('Peak Month')),
                ],
                rows:
                    years.map((year) {
                      final data = yearlyData[year]!;
                      final totalConsumption = _getTotalConsumptionValue(
                        data['totalConsumption'] as Map<MeterType, double>,
                      );
                      final growthRate = data['growthRate'] as double;

                      return DataRow(
                        cells: [
                          DataCell(Text(year.toString())),
                          DataCell(Text('${data['totalReadings']}')),
                          DataCell(Text('${data['buildings']}')),
                          DataCell(Text('${data['rooms']}')),
                          DataCell(Text(totalConsumption.toStringAsFixed(2))),
                          DataCell(
                            Text(
                              '${growthRate.toStringAsFixed(1)}%',
                              style: TextStyle(
                                color:
                                    growthRate > 0
                                        ? Colors.red
                                        : growthRate < 0
                                        ? Colors.green
                                        : Colors.grey,
                              ),
                            ),
                          ),
                          DataCell(Text(data['peakMonth'] ?? 'N/A')),
                        ],
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBuildingOverviewCards(Map<String, dynamic> report) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Total Buildings',
            '${report['totalBuildings']}',
            Icons.business,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Most Efficient',
            report['mostEfficientBuilding'] ?? 'N/A',
            Icons.eco,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Highest Usage',
            report['highestConsumptionBuilding'] ?? 'N/A',
            Icons.trending_up,
            Colors.red,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Avg Rooms',
            '${(report['averageRoomsPerBuilding'] ?? 0.0).toStringAsFixed(1)}',
            Icons.room,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildBuildingComparisonChart(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Building Consumption Comparison',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: _getMaxBuildingConsumption(buildingData) * 1.2,
                  barTouchData: BarTouchData(enabled: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final buildings = buildingData.keys.toList()..sort();
                          if (value.toInt() >= 0 &&
                              value.toInt() < buildings.length) {
                            return Text('Bldg ${buildings[value.toInt()]}');
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  barGroups: _buildBuildingBarGroups(buildingData),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBuildingEfficiencyAnalysis(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    final buildings = buildingData.keys.toList()..sort();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Building Efficiency Analysis',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text(value.toStringAsFixed(0));
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() >= 0 &&
                              value.toInt() < buildings.length) {
                            return Text('Bldg ${buildings[value.toInt()]}');
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _buildEfficiencySpots(buildingData),
                      color: Colors.purple,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(show: true),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.purple.withValues(alpha: 0.3),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBuildingStatisticsTable(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    final buildings = buildingData.keys.toList()..sort();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Building Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('Building')),
                  DataColumn(label: Text('Readings')),
                  DataColumn(label: Text('Rooms')),
                  DataColumn(label: Text('Total Consumption')),
                  DataColumn(label: Text('Avg Consumption')),
                  DataColumn(label: Text('Efficiency')),
                  DataColumn(label: Text('Last Reading')),
                ],
                rows:
                    buildings.map((building) {
                      final data = buildingData[building]!;
                      final totalConsumption = _getTotalConsumptionValue(
                        data['totalConsumption'] as Map<MeterType, double>,
                      );
                      final avgConsumption = _getTotalConsumptionValue(
                        data['averageConsumption'] as Map<MeterType, double>,
                      );
                      final efficiency = data['efficiency'] as double;
                      final lastReading = data['lastReading'] as DateTime?;

                      return DataRow(
                        cells: [
                          DataCell(Text(building)),
                          DataCell(Text('${data['totalReadings']}')),
                          DataCell(Text('${data['rooms']}')),
                          DataCell(Text(totalConsumption.toStringAsFixed(2))),
                          DataCell(Text(avgConsumption.toStringAsFixed(2))),
                          DataCell(Text(efficiency.toStringAsFixed(2))),
                          DataCell(
                            Text(
                              lastReading != null
                                  ? '${lastReading.day}/${lastReading.month}/${lastReading.year}'
                                  : 'N/A',
                            ),
                          ),
                        ],
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendOverviewCards(Map<String, dynamic> report) {
    final trendDirection = report['trendDirection'] as String;
    final seasonalPatterns = report['seasonalPatterns'] as Map<String, double>;
    final predictions = report['predictions'] as List<double>;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Trend Direction',
            _getTrendDisplayText(trendDirection),
            Icons.trending_up,
            _getTrendColor(trendDirection),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Peak Season',
            _getPeakSeason(seasonalPatterns),
            Icons.wb_sunny,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Predictions',
            '${predictions.length} months',
            Icons.timeline,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Data Points',
            '${(report['monthlyTotals'] as Map).length}',
            Icons.data_usage,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildConsumptionTrendsChart(Map<String, dynamic> report) {
    final monthlyTotals = report['monthlyTotals'] as Map<DateTime, double>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Consumption Trends Over Time',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final dates = monthlyTotals.keys.toList()..sort();
                          if (value.toInt() >= 0 &&
                              value.toInt() < dates.length) {
                            final date = dates[value.toInt()];
                            return Text(
                              '${date.month}/${date.year.toString().substring(2)}',
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _buildTrendSpots(monthlyTotals),
                      color: Colors.blue,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(show: true),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.blue.withValues(alpha: 0.3),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSeasonalPatternsChart(Map<String, dynamic> report) {
    final seasonalPatterns = report['seasonalPatterns'] as Map<String, double>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Seasonal Consumption Patterns',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY:
                      seasonalPatterns.values.isNotEmpty
                          ? seasonalPatterns.values.reduce(
                                (a, b) => a > b ? a : b,
                              ) *
                              1.2
                          : 100,
                  barTouchData: BarTouchData(enabled: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          const seasons = [
                            'Spring',
                            'Summer',
                            'Autumn',
                            'Winter',
                          ];
                          if (value.toInt() >= 0 &&
                              value.toInt() < seasons.length) {
                            return Text(seasons[value.toInt()]);
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  barGroups: _buildSeasonalBarGroups(seasonalPatterns),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPredictionsAnalysis(Map<String, dynamic> report) {
    final predictions = report['predictions'] as List<double>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Consumption Predictions',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (predictions.isNotEmpty) ...[
              SizedBox(
                height: 200,
                child: LineChart(
                  LineChartData(
                    gridData: FlGridData(show: true),
                    titlesData: FlTitlesData(
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: true),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            return Text('Month ${(value + 1).toInt()}');
                          },
                        ),
                      ),
                      topTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      rightTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                    ),
                    borderData: FlBorderData(show: true),
                    lineBarsData: [
                      LineChartBarData(
                        spots: _buildPredictionSpots(predictions),
                        color: Colors.orange,
                        barWidth: 3,
                        isStrokeCapRound: true,
                        dotData: FlDotData(show: true),
                        dashArray: [5, 5], // Dashed line for predictions
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Predicted consumption for next ${predictions.length} months based on historical trends',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
              ),
            ] else ...[
              Container(
                height: 100,
                alignment: Alignment.center,
                child: const Text(
                  'Insufficient data for predictions',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Helper methods for trend analysis
  String _getTrendDisplayText(String trend) {
    switch (trend) {
      case 'increasing':
        return 'Increasing';
      case 'decreasing':
        return 'Decreasing';
      case 'stable':
        return 'Stable';
      default:
        return 'Unknown';
    }
  }

  Color _getTrendColor(String trend) {
    switch (trend) {
      case 'increasing':
        return Colors.red;
      case 'decreasing':
        return Colors.green;
      case 'stable':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  double _getTotalConsumptionValue(Map<MeterType, double> consumption) {
    return consumption.values.fold<double>(0, (sum, value) => sum + value);
  }

  double _getMaxYearlyConsumption(Map<int, Map<String, dynamic>> yearlyData) {
    double max = 0;
    for (final data in yearlyData.values) {
      final totalConsumption = _getTotalConsumptionValue(
        data['totalConsumption'] as Map<MeterType, double>,
      );
      if (totalConsumption > max) {
        max = totalConsumption;
      }
    }
    return max;
  }

  List<BarChartGroupData> _buildYearlyBarGroups(
    Map<int, Map<String, dynamic>> yearlyData,
  ) {
    final years = yearlyData.keys.toList()..sort();
    final groups = <BarChartGroupData>[];

    for (int i = 0; i < years.length; i++) {
      final year = years[i];
      final data = yearlyData[year]!;
      final totalConsumption = _getTotalConsumptionValue(
        data['totalConsumption'] as Map<MeterType, double>,
      );

      groups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: totalConsumption,
              color: Colors.blue,
              width: 20,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      );
    }

    return groups;
  }

  List<FlSpot> _buildGrowthRateSpots(
    Map<int, Map<String, dynamic>> yearlyData,
  ) {
    final years = yearlyData.keys.toList()..sort();
    final spots = <FlSpot>[];

    for (int i = 0; i < years.length; i++) {
      final year = years[i];
      final data = yearlyData[year]!;
      final growthRate = data['growthRate'] as double;
      spots.add(FlSpot(i.toDouble(), growthRate));
    }

    return spots;
  }

  double _getMaxBuildingConsumption(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    double max = 0;
    for (final data in buildingData.values) {
      final totalConsumption = _getTotalConsumptionValue(
        data['totalConsumption'] as Map<MeterType, double>,
      );
      if (totalConsumption > max) {
        max = totalConsumption;
      }
    }
    return max;
  }

  List<BarChartGroupData> _buildBuildingBarGroups(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    final buildings = buildingData.keys.toList()..sort();
    final groups = <BarChartGroupData>[];

    for (int i = 0; i < buildings.length; i++) {
      final building = buildings[i];
      final data = buildingData[building]!;
      final totalConsumption = _getTotalConsumptionValue(
        data['totalConsumption'] as Map<MeterType, double>,
      );

      groups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: totalConsumption,
              color: Colors.green,
              width: 20,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      );
    }

    return groups;
  }

  List<FlSpot> _buildEfficiencySpots(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    final buildings = buildingData.keys.toList()..sort();
    final spots = <FlSpot>[];

    for (int i = 0; i < buildings.length; i++) {
      final building = buildings[i];
      final data = buildingData[building]!;
      final efficiency = data['efficiency'] as double;
      spots.add(FlSpot(i.toDouble(), efficiency));
    }

    return spots;
  }

  String _getPeakSeason(Map<String, double> seasonalPatterns) {
    if (seasonalPatterns.isEmpty) return 'N/A';

    final peakEntry = seasonalPatterns.entries.reduce(
      (a, b) => a.value > b.value ? a : b,
    );

    return peakEntry.key;
  }

  List<FlSpot> _buildTrendSpots(Map<DateTime, double> monthlyTotals) {
    final dates = monthlyTotals.keys.toList()..sort();
    final spots = <FlSpot>[];

    for (int i = 0; i < dates.length; i++) {
      final date = dates[i];
      final value = monthlyTotals[date] ?? 0;
      spots.add(FlSpot(i.toDouble(), value));
    }

    return spots;
  }

  List<BarChartGroupData> _buildSeasonalBarGroups(
    Map<String, double> seasonalPatterns,
  ) {
    const seasons = ['Spring', 'Summer', 'Autumn', 'Winter'];
    final groups = <BarChartGroupData>[];

    for (int i = 0; i < seasons.length; i++) {
      final season = seasons[i];
      final value = seasonalPatterns[season] ?? 0;

      groups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: value,
              color: _getSeasonColor(season),
              width: 30,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      );
    }

    return groups;
  }

  List<FlSpot> _buildPredictionSpots(List<double> predictions) {
    final spots = <FlSpot>[];

    for (int i = 0; i < predictions.length; i++) {
      spots.add(FlSpot(i.toDouble(), predictions[i]));
    }

    return spots;
  }

  Color _getSeasonColor(String season) {
    switch (season) {
      case 'Spring':
        return Colors.green;
      case 'Summer':
        return Colors.orange;
      case 'Autumn':
        return Colors.brown;
      case 'Winter':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}

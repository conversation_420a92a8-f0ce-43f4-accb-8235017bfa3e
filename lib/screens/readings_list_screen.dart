import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/reading_provider.dart';

import '../models/meter_reading.dart';
import '../models/meter_type.dart';
import '../services/share_service.dart';
import 'reading_detail_screen.dart';

class ReadingsListScreen extends ConsumerStatefulWidget {
  const ReadingsListScreen({super.key});

  @override
  ConsumerState<ReadingsListScreen> createState() => _ReadingsListScreenState();
}

class _ReadingsListScreenState extends ConsumerState<ReadingsListScreen> {
  String? _selectedBuilding;
  String? _selectedRoom;
  MeterType? _selectedMeterType;
  String _sortBy = 'timestamp'; // timestamp, building, room, meterType
  bool _sortAscending = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Load readings when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(readingProvider.notifier).loadReadings();
    });
  }

  @override
  Widget build(BuildContext context) {
    final readingState = ref.watch(readingProvider);

    // Filter and sort readings
    List<MeterReading> filteredReadings = _filterReadings(
      readingState.readings,
    );
    filteredReadings = _sortReadings(filteredReadings);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Meter Readings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filter readings',
          ),
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: _showSortDialog,
            tooltip: 'Sort readings',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'export',
                    child: Row(
                      children: [
                        Icon(Icons.download),
                        SizedBox(width: 8),
                        Text('Export All'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share),
                        SizedBox(width: 8),
                        Text('Share Readings'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear_filters',
                    child: Row(
                      children: [
                        Icon(Icons.clear_all),
                        SizedBox(width: 8),
                        Text('Clear Filters'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter summary
          _buildSearchAndFilterSection(
            filteredReadings.length,
            readingState.readings.length,
          ),

          // Readings list
          Expanded(
            child: _buildReadingsList(filteredReadings, readingState.isLoading),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.of(context).pop(),
        tooltip: 'Back to Home',
        child: const Icon(Icons.home),
      ),
    );
  }

  Widget _buildSearchAndFilterSection(int filteredCount, int totalCount) {
    final hasFilters =
        _selectedBuilding != null ||
        _selectedRoom != null ||
        _selectedMeterType != null ||
        _searchQuery.isNotEmpty;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            decoration: const InputDecoration(
              hintText: 'Search readings...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value.toLowerCase();
              });
            },
          ),

          const SizedBox(height: 12),

          // Filter summary and count
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  hasFilters
                      ? 'Showing $filteredCount of $totalCount readings'
                      : '$totalCount readings',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
                ),
              ),
              if (hasFilters)
                TextButton.icon(
                  onPressed: _clearAllFilters,
                  icon: const Icon(Icons.clear, size: 16),
                  label: const Text('Clear'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                ),
            ],
          ),

          // Active filters chips
          if (hasFilters) ...[
            const SizedBox(height: 8),
            _buildActiveFiltersChips(),
          ],
        ],
      ),
    );
  }

  Widget _buildActiveFiltersChips() {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: [
        if (_selectedBuilding != null)
          Chip(
            label: Text('Building $_selectedBuilding'),
            onDeleted: () => setState(() => _selectedBuilding = null),
            deleteIcon: const Icon(Icons.close, size: 16),
          ),
        if (_selectedRoom != null)
          Chip(
            label: Text('Room $_selectedRoom'),
            onDeleted: () => setState(() => _selectedRoom = null),
            deleteIcon: const Icon(Icons.close, size: 16),
          ),
        if (_selectedMeterType != null)
          Chip(
            label: Text(_selectedMeterType!.displayName),
            onDeleted: () => setState(() => _selectedMeterType = null),
            deleteIcon: const Icon(Icons.close, size: 16),
          ),
        if (_searchQuery.isNotEmpty)
          Chip(
            label: Text('Search: $_searchQuery'),
            onDeleted: () => setState(() => _searchQuery = ''),
            deleteIcon: const Icon(Icons.close, size: 16),
          ),
      ],
    );
  }

  Widget _buildReadingsList(List<MeterReading> readings, bool isLoading) {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading readings...'),
          ],
        ),
      );
    }

    if (readings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _hasActiveFilters() ? Icons.filter_list_off : Icons.list_alt,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              _hasActiveFilters()
                  ? 'No readings match your filters'
                  : 'No readings recorded yet',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Text(
              _hasActiveFilters()
                  ? 'Try adjusting your filters'
                  : 'Start by taking some meter readings',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade500),
            ),
            if (_hasActiveFilters()) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _clearAllFilters,
                child: const Text('Clear All Filters'),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: readings.length,
      itemBuilder: (context, index) {
        final reading = readings[index];
        return _buildReadingCard(reading);
      },
    );
  }

  Widget _buildReadingCard(MeterReading reading) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _viewReadingDetails(reading),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with meter type and timestamp
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getMeterTypeColor(
                        reading.meterType,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      reading.meterType.icon,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          reading.meterType.displayName,
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _getMeterTypeColor(reading.meterType),
                          ),
                        ),
                        Text(
                          _formatDateTime(reading.timestamp),
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey.shade600),
                        ),
                      ],
                    ),
                  ),
                  Icon(Icons.chevron_right, color: Colors.grey.shade400),
                ],
              ),

              const SizedBox(height: 12),

              // Location and reading value
              Row(
                children: [
                  Expanded(
                    child: _buildInfoChip(
                      Icons.business,
                      'Building ${reading.buildingNumber}',
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildInfoChip(
                      Icons.room,
                      'Room ${reading.roomNumber}',
                      Colors.green,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Reading value
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Reading Value',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '${reading.readingValue} ${reading.meterType.unit}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Notes (if any)
              if (reading.notes?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber.shade200),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.note, size: 16, color: Colors.amber.shade700),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          reading.notes!,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.amber.shade700),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Color _getMeterTypeColor(MeterType meterType) {
    switch (meterType) {
      case MeterType.main:
        return Colors.blue;
      case MeterType.generator:
        return Colors.orange;
      case MeterType.utility:
        return Colors.purple;
      case MeterType.water:
        return Colors.cyan;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Filter and sort methods
  List<MeterReading> _filterReadings(List<MeterReading> readings) {
    return readings.where((reading) {
      // Building filter
      if (_selectedBuilding != null &&
          reading.buildingNumber != _selectedBuilding) {
        return false;
      }

      // Room filter
      if (_selectedRoom != null && reading.roomNumber != _selectedRoom) {
        return false;
      }

      // Meter type filter
      if (_selectedMeterType != null &&
          reading.meterType != _selectedMeterType) {
        return false;
      }

      // Search query filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return reading.buildingNumber.toLowerCase().contains(query) ||
            reading.roomNumber.toLowerCase().contains(query) ||
            reading.meterType.displayName.toLowerCase().contains(query) ||
            reading.readingValue.toString().contains(query) ||
            (reading.notes?.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();
  }

  List<MeterReading> _sortReadings(List<MeterReading> readings) {
    readings.sort((a, b) {
      int comparison;

      switch (_sortBy) {
        case 'building':
          comparison = a.buildingNumber.compareTo(b.buildingNumber);
          break;
        case 'room':
          comparison = a.roomNumber.compareTo(b.roomNumber);
          break;
        case 'meterType':
          comparison = a.meterType.displayName.compareTo(
            b.meterType.displayName,
          );
          break;
        case 'timestamp':
        default:
          comparison = a.timestamp.compareTo(b.timestamp);
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    return readings;
  }

  bool _hasActiveFilters() {
    return _selectedBuilding != null ||
        _selectedRoom != null ||
        _selectedMeterType != null ||
        _searchQuery.isNotEmpty;
  }

  void _clearAllFilters() {
    setState(() {
      _selectedBuilding = null;
      _selectedRoom = null;
      _selectedMeterType = null;
      _searchQuery = '';
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Filter Readings'),
            content: StatefulBuilder(
              builder: (context, setDialogState) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Building filter
                    DropdownButtonFormField<String>(
                      value: _selectedBuilding,
                      decoration: const InputDecoration(
                        labelText: 'Building',
                        border: OutlineInputBorder(),
                      ),
                      items:
                          _getUniqueBuildings().map((building) {
                            return DropdownMenuItem(
                              value: building,
                              child: Text('Building $building'),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setDialogState(() {
                          _selectedBuilding = value;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // Room filter
                    DropdownButtonFormField<String>(
                      value: _selectedRoom,
                      decoration: const InputDecoration(
                        labelText: 'Room',
                        border: OutlineInputBorder(),
                      ),
                      items:
                          _getUniqueRooms().map((room) {
                            return DropdownMenuItem(
                              value: room,
                              child: Text('Room $room'),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setDialogState(() {
                          _selectedRoom = value;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // Meter type filter
                    DropdownButtonFormField<MeterType>(
                      value: _selectedMeterType,
                      decoration: const InputDecoration(
                        labelText: 'Meter Type',
                        border: OutlineInputBorder(),
                      ),
                      items:
                          MeterType.values.map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Row(
                                children: [
                                  Text(type.icon),
                                  const SizedBox(width: 8),
                                  Text(type.displayName),
                                ],
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setDialogState(() {
                          _selectedMeterType = value;
                        });
                      },
                    ),
                  ],
                );
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    // Filters are already set in the dialog
                  });
                  Navigator.of(context).pop();
                },
                child: const Text('Apply'),
              ),
            ],
          ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sort Readings'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('Timestamp'),
                  value: 'timestamp',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                    Navigator.of(context).pop();
                  },
                ),
                RadioListTile<String>(
                  title: const Text('Building'),
                  value: 'building',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                    Navigator.of(context).pop();
                  },
                ),
                RadioListTile<String>(
                  title: const Text('Room'),
                  value: 'room',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                    Navigator.of(context).pop();
                  },
                ),
                RadioListTile<String>(
                  title: const Text('Meter Type'),
                  value: 'meterType',
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                    Navigator.of(context).pop();
                  },
                ),
                const Divider(),
                SwitchListTile(
                  title: const Text('Ascending Order'),
                  value: _sortAscending,
                  onChanged: (value) {
                    setState(() {
                      _sortAscending = value;
                    });
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportReadings();
        break;
      case 'share':
        _shareReadings();
        break;
      case 'clear_filters':
        _clearAllFilters();
        break;
    }
  }

  void _exportReadings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality will be implemented'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Future<void> _shareReadings() async {
    try {
      final readingState = ref.read(readingProvider);
      final filteredReadings = _sortReadings(
        _filterReadings(readingState.readings),
      );

      if (filteredReadings.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No readings to share'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      final shareService = ShareService();

      // Convert readings to shareable format
      final readingData =
          filteredReadings
              .map(
                (reading) => {
                  'buildingNumber': reading.buildingNumber,
                  'roomNumber': reading.roomNumber,
                  'meterType': reading.meterType.displayName,
                  'readingValue': reading.readingValue,
                  'unit': reading.meterType.unit,
                  'timestamp': _formatDateTime(reading.timestamp),
                  'notes': reading.notes ?? '',
                },
              )
              .toList();

      final title =
          _hasActiveFilters()
              ? 'Filtered Meter Readings (${filteredReadings.length} readings)'
              : 'All Meter Readings (${filteredReadings.length} readings)';

      await shareService.shareReadingsAsText(readingData, title: title);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Shared ${filteredReadings.length} readings'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share readings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _viewReadingDetails(MeterReading reading) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => ReadingDetailScreen(reading: reading)),
    );
  }

  List<String> _getUniqueBuildings() {
    final readings = ref.read(readingProvider).readings;
    return readings.map((r) => r.buildingNumber).toSet().toList()..sort();
  }

  List<String> _getUniqueRooms() {
    final readings = ref.read(readingProvider).readings;
    return readings.map((r) => r.roomNumber).toSet().toList()..sort();
  }
}

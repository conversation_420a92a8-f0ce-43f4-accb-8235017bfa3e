import 'package:flutter/material.dart';

class ReadingsListScreen extends StatefulWidget {
  const ReadingsListScreen({super.key});

  @override
  State<ReadingsListScreen> createState() => _ReadingsListScreenState();
}

class _ReadingsListScreenState extends State<ReadingsListScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Readings List'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.list, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('Readings list functionality will be implemented here'),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../providers/building_provider.dart';
import '../providers/reading_provider.dart';
import 'building_selection_screen.dart';
import 'room_entry_screen.dart';
import 'readings_list_screen.dart';
import 'export_screen.dart';
import 'import_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Meter Reading App'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context),
          ),
        ],
      ),
      body: Consumer3<AppStateProvider, BuildingProvider, ReadingProvider>(
        builder: (context, appState, buildingProvider, readingProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Current State Card
                _buildCurrentStateCard(buildingProvider),
                const SizedBox(height: 16),
                
                // Quick Actions
                _buildQuickActionsSection(context, buildingProvider),
                const SizedBox(height: 16),
                
                // Statistics Card
                _buildStatisticsCard(readingProvider),
                const SizedBox(height: 16),
                
                // Data Management Section
                _buildDataManagementSection(context),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentStateCard(BuildingProvider buildingProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Current Location',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              buildingProvider.currentStateDisplay,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            if (buildingProvider.lastAccessTime != null) ...[
              const SizedBox(height: 8),
              Text(
                'Last updated: ${_formatDateTime(buildingProvider.lastAccessTime!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _navigateToBuildingSelection(context),
                    icon: const Icon(Icons.business),
                    label: Text(buildingProvider.hasBuildingSelected 
                        ? 'Change Building' 
                        : 'Select Building'),
                  ),
                ),
                if (buildingProvider.hasBuildingSelected) ...[
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _navigateToRoomEntry(context),
                      icon: const Icon(Icons.room),
                      label: const Text('Enter Room'),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection(BuildContext context, BuildingProvider buildingProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildQuickActionCard(
              context,
              icon: Icons.camera_alt,
              title: 'Start Reading',
              subtitle: 'Take meter photos',
              onTap: buildingProvider.isStateComplete
                  ? () => _navigateToRoomEntry(context)
                  : () => _navigateToBuildingSelection(context),
              enabled: true,
            ),
            _buildQuickActionCard(
              context,
              icon: Icons.list,
              title: 'View Readings',
              subtitle: 'Browse all data',
              onTap: () => _navigateToReadingsList(context),
              enabled: true,
            ),
            _buildQuickActionCard(
              context,
              icon: Icons.upload,
              title: 'Export Data',
              subtitle: 'Create backup',
              onTap: () => _navigateToExport(context),
              enabled: true,
            ),
            _buildQuickActionCard(
              context,
              icon: Icons.download,
              title: 'Import Data',
              subtitle: 'Merge from file',
              onTap: () => _navigateToImport(context),
              enabled: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required bool enabled,
  }) {
    return Card(
      child: InkWell(
        onTap: enabled ? onTap : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: enabled ? Theme.of(context).primaryColor : Colors.grey,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: enabled ? null : Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: enabled ? Colors.grey[600] : Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(ReadingProvider readingProvider) {
    final stats = readingProvider.getStatistics();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Statistics',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Readings',
                    '${stats['totalReadings']}',
                    Icons.assessment,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Buildings',
                    '${stats['totalBuildings']}',
                    Icons.business,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Rooms',
                    '${stats['totalRooms']}',
                    Icons.room,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDataManagementSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Data Management',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.upload_file),
                title: const Text('Export Data'),
                subtitle: const Text('Create ZIP backup with images'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => _navigateToExport(context),
              ),
              const Divider(height: 1),
              ListTile(
                leading: const Icon(Icons.download),
                title: const Text('Import Data'),
                subtitle: const Text('Merge data from other devices'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => _navigateToImport(context),
              ),
              const Divider(height: 1),
              ListTile(
                leading: const Icon(Icons.list_alt),
                title: const Text('View All Readings'),
                subtitle: const Text('Browse and manage readings'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () => _navigateToReadingsList(context),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Navigation methods
  void _navigateToBuildingSelection(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const BuildingSelectionScreen()),
    );
  }

  void _navigateToRoomEntry(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const RoomEntryScreen()),
    );
  }

  void _navigateToReadingsList(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const ReadingsListScreen()),
    );
  }

  void _navigateToExport(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const ExportScreen()),
    );
  }

  void _navigateToImport(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const ImportScreen()),
    );
  }

  // Utility methods
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Settings'),
        content: const Text('Settings functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

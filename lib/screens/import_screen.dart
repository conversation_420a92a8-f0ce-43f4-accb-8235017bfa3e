import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/reading_provider.dart';
import '../services/import_service.dart';

class ImportScreen extends ConsumerStatefulWidget {
  const ImportScreen({super.key});

  @override
  ConsumerState<ImportScreen> createState() => _ImportScreenState();
}

class _ImportScreenState extends ConsumerState<ImportScreen> {
  final ImportService _importService = ImportService();

  bool _isImporting = false;
  bool _allowDuplicates = false;
  String? _selectedFilePath;
  Map<String, dynamic>? _fileValidation;
  ImportResult? _lastImportResult;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Import Data'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
            tooltip: 'Help',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // File selection card
            _buildFileSelectionCard(),
            const SizedBox(height: 16),

            // File validation info (if file selected)
            if (_fileValidation != null) ...[
              _buildFileValidationCard(),
              const SizedBox(height: 16),
            ],

            // Import options card (if valid file selected)
            if (_fileValidation?['isValid'] == true) ...[
              _buildImportOptionsCard(),
              const SizedBox(height: 16),
            ],

            // Import button (if valid file selected)
            if (_fileValidation?['isValid'] == true) ...[
              _buildImportButton(),
              const SizedBox(height: 16),
            ],

            // Import result (if import completed)
            if (_lastImportResult != null) ...[
              _buildImportResultCard(),
              const SizedBox(height: 16),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFileSelectionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.file_upload, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Select Import File',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_selectedFilePath == null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: const Column(
                  children: [
                    Icon(Icons.cloud_upload, size: 48, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'Select a ZIP file to import',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Choose an export file created by this app',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _selectFile,
                icon: const Icon(Icons.folder_open),
                label: const Text('Browse Files'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ] else ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.archive, color: Colors.blue),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _selectedFilePath!.split('/').last,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Path: $_selectedFilePath',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _selectFile,
                      icon: const Icon(Icons.folder_open),
                      label: const Text('Choose Different File'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _clearSelectedFile,
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFileValidationCard() {
    final validation = _fileValidation!;
    final isValid = validation['isValid'] as bool;
    final errors = validation['errors'] as List<String>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isValid ? Icons.check_circle : Icons.error,
                  color: isValid ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'File Validation',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (isValid) ...[
              _buildValidationItem(
                Icons.check,
                'Valid export file',
                Colors.green,
              ),
              _buildValidationItem(
                Icons.data_object,
                '${validation['readingsCount']} readings found',
                Colors.blue,
              ),
              if (validation['hasImages'])
                _buildValidationItem(
                  Icons.image,
                  '${validation['imagesCount']} images found',
                  Colors.blue,
                ),
            ] else ...[
              _buildValidationItem(
                Icons.error,
                'Invalid file format',
                Colors.red,
              ),
              ...errors.map(
                (error) =>
                    _buildValidationItem(Icons.warning, error, Colors.orange),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildValidationItem(IconData icon, String text, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Expanded(child: Text(text, style: TextStyle(color: color))),
        ],
      ),
    );
  }

  Widget _buildImportOptionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Import Options',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Allow Duplicate Readings'),
              subtitle: const Text(
                'Import readings even if they already exist',
              ),
              value: _allowDuplicates,
              onChanged: (value) {
                setState(() {
                  _allowDuplicates = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImportButton() {
    return ElevatedButton.icon(
      onPressed: !_isImporting ? _performImport : null,
      icon:
          _isImporting
              ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
              : const Icon(Icons.upload),
      label: Text(_isImporting ? 'Importing...' : 'Import Data'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    );
  }

  Widget _buildImportResultCard() {
    final result = _lastImportResult!;
    final isSuccess = result.isSuccessful;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isSuccess ? Icons.check_circle : Icons.error,
                  color: isSuccess ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Import Result',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildResultItem(
              Icons.assessment,
              'Total Readings',
              '${result.totalReadings}',
              Colors.blue,
            ),
            _buildResultItem(
              Icons.check,
              'Successfully Imported',
              '${result.importedReadings}',
              Colors.green,
            ),
            if (result.duplicateReadings > 0)
              _buildResultItem(
                Icons.content_copy,
                'Duplicates Found',
                '${result.duplicateReadings}',
                Colors.orange,
              ),
            if (result.skippedReadings > 0)
              _buildResultItem(
                Icons.skip_next,
                'Skipped',
                '${result.skippedReadings}',
                Colors.grey,
              ),

            if (result.hasErrors) ...[
              const SizedBox(height: 16),
              Text(
                'Errors:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 8),
              ...result.errors.map(
                (error) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.error, size: 16, color: Colors.red),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          error,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            if (isSuccess) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _refreshReadings,
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh Readings List'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(
    IconData icon,
    String label,
    String value,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            value,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
          ),
        ],
      ),
    );
  }

  Future<void> _selectFile() async {
    // For now, we'll use a simple text input dialog to simulate file selection
    // In a real implementation, you would use file_picker package
    final result = await showDialog<String>(
      context: context,
      builder: (context) {
        String filePath = '';
        return AlertDialog(
          title: const Text('Select Import File'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Enter the path to your export ZIP file:'),
              const SizedBox(height: 16),
              TextField(
                onChanged: (value) => filePath = value,
                decoration: const InputDecoration(
                  hintText: '/path/to/export.zip',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(filePath),
              child: const Text('Select'),
            ),
          ],
        );
      },
    );

    if (result != null && result.isNotEmpty) {
      setState(() {
        _selectedFilePath = result;
        _fileValidation = null;
        _lastImportResult = null;
      });
      await _validateFile();
    }
  }

  void _clearSelectedFile() {
    setState(() {
      _selectedFilePath = null;
      _fileValidation = null;
      _lastImportResult = null;
    });
  }

  Future<void> _validateFile() async {
    if (_selectedFilePath == null) return;

    try {
      final validation = await _importService.validateZipFile(
        _selectedFilePath!,
      );
      setState(() {
        _fileValidation = validation;
      });
    } catch (e) {
      setState(() {
        _fileValidation = {
          'isValid': false,
          'errors': ['Failed to validate file: $e'],
        };
      });
    }
  }

  Future<void> _performImport() async {
    if (_selectedFilePath == null) return;

    setState(() {
      _isImporting = true;
      _lastImportResult = null;
    });

    try {
      final result = await _importService.importFromZipFile(
        _selectedFilePath!,
        allowDuplicates: _allowDuplicates,
      );

      setState(() {
        _lastImportResult = result;
      });

      if (mounted) {
        if (result.isSuccessful) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Successfully imported ${result.importedReadings} readings',
              ),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Import completed with ${result.errors.length} errors',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Import failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isImporting = false;
      });
    }
  }

  Future<void> _refreshReadings() async {
    await ref.read(readingProvider.notifier).loadReadings();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Readings list refreshed'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Import Help'),
            content: const SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'File Selection:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• Select a ZIP file created by the export function'),
                  Text('• File must contain readings data and images'),
                  Text('• Only valid export files can be imported'),
                  SizedBox(height: 16),
                  Text(
                    'Import Options:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Allow Duplicates: Import readings even if they exist',
                  ),
                  Text('• Skip Duplicates: Only import new readings'),
                  SizedBox(height: 16),
                  Text(
                    'Import Process:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• File is validated before import'),
                  Text('• Images are copied to app storage'),
                  Text('• Readings are added to database'),
                  Text('• Import results are displayed'),
                  SizedBox(height: 16),
                  Text('Note:', style: TextStyle(fontWeight: FontWeight.bold)),
                  SizedBox(height: 8),
                  Text('• Import process cannot be undone'),
                  Text('• Backup your data before importing'),
                  Text('• Large files may take time to process'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }
}

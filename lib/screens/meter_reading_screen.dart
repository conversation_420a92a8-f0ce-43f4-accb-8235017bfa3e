import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/building_provider.dart';
import '../models/meter_type.dart';
import 'camera_screen.dart';

class MeterReadingScreen extends ConsumerWidget {
  const MeterReadingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final buildingState = ref.watch(buildingProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Meter Reading'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.camera_alt, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text('Meter Reading Screen'),
            const SizedBox(height: 8),
            if (buildingState.selectedBuildingNumber != null)
              Text('Building: ${buildingState.selectedBuildingNumber}'),
            if (buildingState.currentRoomNumber != null)
              Text('Room: ${buildingState.currentRoomNumber}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => CameraScreen(meterType: MeterType.main),
                ),
              ),
              child: const Text('Take Photo'),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/building_provider.dart';
import '../providers/reading_provider.dart';
import '../models/meter_type.dart';
import '../models/meter_reading.dart';
import 'camera_screen.dart';

class MeterReadingScreen extends ConsumerWidget {
  const MeterReadingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final buildingState = ref.watch(buildingProvider);
    final readingState = ref.watch(readingProvider);

    if (buildingState.selectedBuildingNumber == null ||
        buildingState.currentRoomNumber == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Meter Reading')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.warning, size: 64, color: Colors.orange),
              SizedBox(height: 16),
              Text('Incomplete setup'),
              Text('Please select building and room first'),
            ],
          ),
        ),
      );
    }

    final buildingNumber = buildingState.selectedBuildingNumber!;
    final roomNumber = buildingState.currentRoomNumber!;

    // Get readings for current room
    final roomReadings =
        readingState.readings
            .where(
              (reading) =>
                  reading.buildingNumber == buildingNumber &&
                  reading.roomNumber == roomNumber,
            )
            .toList();

    final recordedTypes = roomReadings.map((r) => r.meterType).toSet();
    final missingTypes =
        MeterType.values
            .where((type) => !recordedTypes.contains(type))
            .toList();
    final nextType = missingTypes.isNotEmpty ? missingTypes.first : null;
    final completion = recordedTypes.length / MeterType.values.length;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Meter Reading'),
        actions: [
          IconButton(
            icon: const Icon(Icons.list),
            onPressed: () => _showRoomReadings(context, roomReadings),
            tooltip: 'View room readings',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Location info card
            _buildLocationCard(
              context,
              buildingNumber,
              roomNumber,
              completion,
            ),
            const SizedBox(height: 16),
      
            // Next meter to read (if any)
            if (nextType != null) ...[
              _buildNextMeterCard(context, nextType, ref),
              const SizedBox(height: 16),
            ],
      
            // Meter types grid
            Text(
              'Meter Types',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 1.2,
                ),
                itemCount: MeterType.values.length,
                itemBuilder: (context, index) {
                  final meterType = MeterType.values[index];
                  final isCompleted = recordedTypes.contains(meterType);
                  final isNext = meterType == nextType;
                  final reading =
                      roomReadings
                          .where((r) => r.meterType == meterType)
                          .firstOrNull;
      
                  return _buildMeterTypeCard(
                    context,
                    meterType,
                    isCompleted,
                    isNext,
                    reading,
                    ref,
                  );
                },
              ),
            ),
      
            // Room completion status
            if (completion == 1.0) ...[
              const SizedBox(height: 16),
              _buildCompletionCard(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLocationCard(
    BuildContext context,
    String buildingNumber,
    String roomNumber,
    double completion,
  ) {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Location',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: Colors.blue.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Building $buildingNumber, Room $roomNumber',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Progress'),
                Text('${(completion * 100).round()}%'),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: completion,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                completion == 1.0 ? Colors.green : Colors.blue,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNextMeterCard(
    BuildContext context,
    MeterType nextType,
    WidgetRef ref,
  ) {
    return Card(
      color: Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Text(nextType.icon, style: const TextStyle(fontSize: 24)),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Next: ${nextType.displayName} Meter',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                      Text(
                        'Unit: ${nextType.unit}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.green.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _navigateToCamera(context, nextType),
              icon: const Icon(Icons.camera_alt),
              label: const Text('Take Photo'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMeterTypeCard(
    BuildContext context,
    MeterType meterType,
    bool isCompleted,
    bool isNext,
    MeterReading? reading,
    WidgetRef ref,
  ) {
    Color cardColor;
    Color textColor;
    IconData statusIcon;

    if (isCompleted) {
      cardColor = Colors.green.shade50;
      textColor = Colors.green.shade700;
      statusIcon = Icons.check_circle;
    } else if (isNext) {
      cardColor = Colors.orange.shade50;
      textColor = Colors.orange.shade700;
      statusIcon = Icons.radio_button_unchecked;
    } else {
      cardColor = Colors.grey.shade50;
      textColor = Colors.grey.shade600;
      statusIcon = Icons.radio_button_unchecked;
    }

    return Card(
      color: cardColor,
      child: InkWell(
        onTap:
            isCompleted
                ? () => _showReadingDetails(context, reading!)
                : () => _navigateToCamera(context, meterType),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(meterType.icon, style: const TextStyle(fontSize: 24)),
                  Icon(statusIcon, color: textColor),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                meterType.displayName,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                meterType.unit,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: textColor),
              ),
              if (isCompleted && reading != null) ...[
                const SizedBox(height: 4),
                Text(
                  '${reading.readingValue}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: textColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompletionCard(BuildContext context) {
    return Card(
      color: Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 48),
            const SizedBox(height: 12),
            Text(
              'Room Complete!',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
            const SizedBox(height: 8),
            const Text('All meter readings have been recorded.'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed:
                  () =>
                      Navigator.of(context).popUntil((route) => route.isFirst),
              child: const Text('Return to Home'),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToCamera(BuildContext context, MeterType meterType) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => CameraScreen(meterType: meterType)),
    );
  }

  void _showReadingDetails(BuildContext context, MeterReading reading) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('${reading.meterType.displayName} Meter'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Reading: ${reading.readingValue} ${reading.meterType.unit}',
                ),
                const SizedBox(height: 8),
                Text('Recorded: ${_formatDateTime(reading.timestamp)}'),
                if (reading.notes?.isNotEmpty == true) ...[
                  const SizedBox(height: 8),
                  Text('Notes: ${reading.notes}'),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _navigateToCamera(context, reading.meterType);
                },
                child: const Text('Retake'),
              ),
            ],
          ),
    );
  }

  void _showRoomReadings(BuildContext context, List<MeterReading> readings) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Room Readings'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: readings.length,
                itemBuilder: (context, index) {
                  final reading = readings[index];
                  return ListTile(
                    leading: Text(reading.meterType.icon),
                    title: Text(reading.meterType.displayName),
                    subtitle: Text(
                      '${reading.readingValue} ${reading.meterType.unit}',
                    ),
                    trailing: Text(_formatDateTime(reading.timestamp)),
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

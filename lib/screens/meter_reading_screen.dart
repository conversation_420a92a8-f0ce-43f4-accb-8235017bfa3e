import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/building_provider.dart';
import '../providers/reading_provider.dart';
import '../models/meter_type.dart';
import 'camera_screen.dart';

class MeterReadingScreen extends StatefulWidget {
  const MeterReadingScreen({super.key});

  @override
  State<MeterReadingScreen> createState() => _MeterReadingScreenState();
}

class _MeterReadingScreenState extends State<MeterReadingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Meter Reading')),
      body: Consumer2<BuildingProvider, ReadingProvider>(
        builder: (context, buildingProvider, readingProvider, child) {
          if (!buildingProvider.isStateComplete) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.warning, size: 64, color: Colors.orange),
                  SizedBox(height: 16),
                  Text('Incomplete setup'),
                  Text('Please select building and room first'),
                ],
              ),
            );
          }

          final buildingNumber = buildingProvider.selectedBuildingNumber!;
          final roomNumber = buildingProvider.currentRoomNumber!;
          final roomReadings = readingProvider.getReadingsForRoom(
            buildingNumber,
            roomNumber,
          );

          final nextType = readingProvider.getNextMeterType(
            buildingNumber,
            roomNumber,
          );
          final completion = readingProvider.getRoomCompletionPercentage(
            buildingNumber,
            roomNumber,
          );

          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Location info
                Card(
                  color: Colors.blue.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Location',
                          style: Theme.of(
                            context,
                          ).textTheme.titleSmall?.copyWith(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Building $buildingNumber, Room $roomNumber',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Progress'),
                            Text('${(completion * 100).round()}%'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: completion,
                          backgroundColor: Colors.grey.shade300,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            completion == 1.0 ? Colors.green : Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Next meter to read
                if (nextType != null) ...[
                  Card(
                    color: Colors.green.shade50,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Row(
                            children: [
                              Text(
                                nextType.icon,
                                style: const TextStyle(fontSize: 24),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Next: ${nextType.displayName} Meter',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green.shade700,
                                      ),
                                    ),
                                    Text(
                                      'Unit: ${nextType.unit}',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyMedium?.copyWith(
                                        color: Colors.green.shade600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: () => _navigateToCamera(nextType),
                            icon: const Icon(Icons.camera_alt),
                            label: const Text('Take Photo'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Meter types grid
                Text(
                  'Meter Types',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Expanded(
                  child: GridView.builder(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 1.2,
                        ),
                    itemCount: MeterType.values.length,
                    itemBuilder: (context, index) {
                      final meterType = MeterType.values[index];
                      final isCompleted = roomReadings.any(
                        (r) => r.meterType == meterType,
                      );
                      final isNext = meterType == nextType;

                      return _buildMeterTypeCard(
                        meterType,
                        isCompleted,
                        isNext,
                        roomReadings
                            .where((r) => r.meterType == meterType)
                            .firstOrNull,
                      );
                    },
                  ),
                ),

                // Room completion status
                if (completion == 1.0) ...[
                  const SizedBox(height: 16),
                  Card(
                    color: Colors.green.shade50,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 48,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Room Complete!',
                            style: Theme.of(
                              context,
                            ).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text('All meter readings have been recorded.'),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed:
                                () => Navigator.of(
                                  context,
                                ).popUntil((route) => route.isFirst),
                            child: const Text('Return to Home'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMeterTypeCard(
    MeterType meterType,
    bool isCompleted,
    bool isNext,
    dynamic reading,
  ) {
    Color cardColor;
    Color textColor;
    IconData statusIcon;

    if (isCompleted) {
      cardColor = Colors.green.shade50;
      textColor = Colors.green.shade700;
      statusIcon = Icons.check_circle;
    } else if (isNext) {
      cardColor = Colors.orange.shade50;
      textColor = Colors.orange.shade700;
      statusIcon = Icons.radio_button_unchecked;
    } else {
      cardColor = Colors.grey.shade50;
      textColor = Colors.grey.shade600;
      statusIcon = Icons.radio_button_unchecked;
    }

    return Card(
      color: cardColor,
      child: InkWell(
        onTap: isCompleted ? null : () => _navigateToCamera(meterType),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(meterType.icon, style: const TextStyle(fontSize: 24)),
                  Icon(statusIcon, color: textColor),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                meterType.displayName,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                meterType.unit,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: textColor),
              ),
              if (isCompleted && reading != null) ...[
                const SizedBox(height: 4),
                Text(
                  '${reading.readingValue}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: textColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCamera(MeterType meterType) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => CameraScreen(meterType: meterType)),
    );
  }
}

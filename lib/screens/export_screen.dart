import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import '../providers/reading_provider.dart';
import '../services/export_service.dart';
import '../services/share_service.dart';

class ExportScreen extends ConsumerStatefulWidget {
  const ExportScreen({super.key});

  @override
  ConsumerState<ExportScreen> createState() => _ExportScreenState();
}

class _ExportScreenState extends ConsumerState<ExportScreen> {
  final ExportService _exportService = ExportService();
  final ShareService _shareService = ShareService();

  bool _isExporting = false;
  String _exportType = 'all';
  String? _selectedBuilding;
  DateTime? _startDate;
  DateTime? _endDate;
  List<File> _exportFiles = [];
  bool _isLoadingFiles = false;

  @override
  void initState() {
    super.initState();
    _loadExportFiles();
  }

  Future<void> _loadExportFiles() async {
    setState(() {
      _isLoadingFiles = true;
    });

    try {
      final files = await _exportService.getExportFiles();
      setState(() {
        _exportFiles = files;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load export files: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoadingFiles = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final readingState = ref.watch(readingProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Export Data'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
            tooltip: 'Help',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Export options card
            _buildExportOptionsCard(readingState),
            const SizedBox(height: 16),

            // Export button
            _buildExportButton(),
            const SizedBox(height: 32),

            // Previous exports section
            _buildPreviousExportsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildExportOptionsCard(readingState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Export Options',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Export type selection
            Text(
              'Export Type',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            RadioListTile<String>(
              title: const Text('All Readings'),
              subtitle: Text(
                'Export all ${readingState.readings.length} readings',
              ),
              value: 'all',
              groupValue: _exportType,
              onChanged: (value) {
                setState(() {
                  _exportType = value!;
                });
              },
            ),

            RadioListTile<String>(
              title: const Text('By Building'),
              subtitle: const Text('Export readings from specific building'),
              value: 'building',
              groupValue: _exportType,
              onChanged: (value) {
                setState(() {
                  _exportType = value!;
                });
              },
            ),

            RadioListTile<String>(
              title: const Text('By Date Range'),
              subtitle: const Text('Export readings within date range'),
              value: 'dateRange',
              groupValue: _exportType,
              onChanged: (value) {
                setState(() {
                  _exportType = value!;
                });
              },
            ),

            // Building selection (if building export type selected)
            if (_exportType == 'building') ...[
              const SizedBox(height: 16),
              _buildBuildingSelection(readingState),
            ],

            // Date range selection (if date range export type selected)
            if (_exportType == 'dateRange') ...[
              const SizedBox(height: 16),
              _buildDateRangeSelection(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBuildingSelection(readingState) {
    final buildings =
        readingState.readings.map((r) => r.buildingNumber).toSet().toList()
          ..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Building',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedBuilding,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'Choose a building',
          ),
          items:
              buildings.map((building) {
                final buildingReadings =
                    readingState.readings
                        .where((r) => r.buildingNumber == building)
                        .length;
                return DropdownMenuItem(
                  value: building,
                  child: Text(
                    'Building $building ($buildingReadings readings)',
                  ),
                );
              }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedBuilding = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDateRangeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Date Range',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _selectDate(true),
                icon: const Icon(Icons.calendar_today),
                label: Text(
                  _startDate != null ? _formatDate(_startDate!) : 'Start Date',
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _selectDate(false),
                icon: const Icon(Icons.calendar_today),
                label: Text(
                  _endDate != null ? _formatDate(_endDate!) : 'End Date',
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildExportButton() {
    final canExport =
        _exportType == 'all' ||
        (_exportType == 'building' && _selectedBuilding != null) ||
        (_exportType == 'dateRange' && _startDate != null && _endDate != null);

    return ElevatedButton.icon(
      onPressed: canExport && !_isExporting ? _performExport : null,
      icon:
          _isExporting
              ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
              : const Icon(Icons.download),
      label: Text(_isExporting ? 'Exporting...' : 'Export Data'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    );
  }

  Widget _buildPreviousExportsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Icons.history, color: Colors.blue),
                    const SizedBox(width: 8),
                    Text(
                      'Previous Exports',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadExportFiles,
                  tooltip: 'Refresh',
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_isLoadingFiles)
              const Center(child: CircularProgressIndicator())
            else if (_exportFiles.isEmpty)
              const Center(
                child: Column(
                  children: [
                    Icon(Icons.folder_open, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text('No export files found'),
                  ],
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _exportFiles.length,
                itemBuilder: (context, index) {
                  final file = _exportFiles[index];
                  return _buildExportFileCard(file);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExportFileCard(File file) {
    return FutureBuilder<Map<String, dynamic>?>(
      future: _exportService.getExportFileInfo(file.path),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Card(child: ListTile(title: Text('Loading...')));
        }

        final info = snapshot.data!;
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const Icon(Icons.archive, color: Colors.green),
            title: Text(
              info['fileName'],
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Size: ${info['formattedSize']}'),
                Text('Created: ${_formatDateTime(info['createdDate'])}'),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (action) => _handleExportFileAction(action, file),
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'share',
                      child: Row(
                        children: [
                          Icon(Icons.share),
                          SizedBox(width: 8),
                          Text('Share File'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'share_info',
                      child: Row(
                        children: [
                          Icon(Icons.info_outline),
                          SizedBox(width: 8),
                          Text('Share Info'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _performExport() async {
    setState(() {
      _isExporting = true;
    });

    try {
      String exportPath;

      switch (_exportType) {
        case 'all':
          exportPath = await _exportService.exportAllReadings();
          break;
        case 'building':
          exportPath = await _exportService.exportReadingsByBuilding(
            _selectedBuilding!,
          );
          break;
        case 'dateRange':
          exportPath = await _exportService.exportReadingsByDateRange(
            _startDate!,
            _endDate!,
          );
          break;
        default:
          throw Exception('Invalid export type');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export completed successfully'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'View',
              onPressed: () => _showExportSuccessDialog(exportPath),
            ),
          ),
        );

        // Refresh the export files list
        await _loadExportFiles();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  void _showExportSuccessDialog(String exportPath) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Export Successful'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Your data has been exported successfully.'),
                const SizedBox(height: 16),
                Text(
                  'File location:',
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  exportPath,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(fontFamily: 'monospace'),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _shareExportFile(exportPath);
                },
                child: const Text('Share'),
              ),
            ],
          ),
    );
  }

  Future<void> _shareExportFile(String filePath) async {
    try {
      final result = await _shareService.shareExportFile(filePath);

      if (mounted) {
        if (result.status == ShareResultStatus.success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Export file shared successfully'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (result.status == ShareResultStatus.dismissed) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Share cancelled'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleExportFileAction(String action, File file) {
    switch (action) {
      case 'share':
        _shareExportFile(file.path);
        break;
      case 'share_info':
        _shareExportFileInfo(file);
        break;
      case 'delete':
        _deleteExportFile(file);
        break;
    }
  }

  Future<void> _shareExportFileInfo(File file) async {
    try {
      final info = await _exportService.getExportFileInfo(file.path);
      if (info != null) {
        await _shareService.shareExportInfo(info);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Export file information shared'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share file info: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteExportFile(File file) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Export File'),
            content: Text(
              'Are you sure you want to delete ${file.path.split('/').last}?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await _exportService.deleteExportFile(file.path);
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Export file deleted'),
              backgroundColor: Colors.green,
            ),
          );
          await _loadExportFiles();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete export file'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Export Help'),
            content: const SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Export Options:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• All Readings: Export all meter readings with images'),
                  Text(
                    '• By Building: Export readings from a specific building',
                  ),
                  Text('• By Date Range: Export readings within a date range'),
                  SizedBox(height: 16),
                  Text(
                    'Export Format:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• ZIP file containing JSON data and images'),
                  Text('• Compatible with import functionality'),
                  Text('• Includes metadata and reading information'),
                  SizedBox(height: 16),
                  Text(
                    'File Management:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• Previous exports are listed below'),
                  Text('• Share or delete export files'),
                  Text('• Files are stored in app documents folder'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/building_provider.dart';

class BuildingSelectionScreen extends ConsumerStatefulWidget {
  const BuildingSelectionScreen({super.key});

  @override
  ConsumerState<BuildingSelectionScreen> createState() =>
      _BuildingSelectionScreenState();
}

class _BuildingSelectionScreenState
    extends ConsumerState<BuildingSelectionScreen> {
  final _buildingController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  List<String> _suggestedBuildings = [];

  @override
  void initState() {
    super.initState();
    _loadSuggestedBuildings();
  }

  @override
  void dispose() {
    _buildingController.dispose();
    super.dispose();
  }

  Future<void> _loadSuggestedBuildings() async {
    final suggestions =
        await ref.read(buildingProvider.notifier).getSuggestedBuildings();
    setState(() {
      _suggestedBuildings = suggestions;
    });
  }

  @override
  Widget build(BuildContext context) {
    final buildingState = ref.watch(buildingProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Select Building')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Current selection display
              if (buildingState.selectedBuildingNumber != null) ...[
                Card(
                  color: Colors.blue.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Currently Selected',
                          style: Theme.of(
                            context,
                          ).textTheme.titleSmall?.copyWith(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Building ${buildingState.selectedBuildingNumber}',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Building number input
              TextFormField(
                controller: _buildingController,
                decoration: const InputDecoration(
                  labelText: 'Building Number',
                  hintText: 'Enter building number (1-9999)',
                  prefixIcon: Icon(Icons.business),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a building number';
                  }
                  if (!_isValidBuildingNumber(value)) {
                    return 'Please enter a valid building number (1-9999)';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Submit button
              ElevatedButton(
                onPressed: _selectBuilding,
                child: const Text('Select Building'),
              ),

              // Suggested buildings
              if (_suggestedBuildings.isNotEmpty) ...[
                const SizedBox(height: 32),
                Text(
                  'Previously Used Buildings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),

                // Use a fixed height container for the list
                SizedBox(
                  height: 200, // Fixed height to avoid overflow
                  child: ListView.builder(
                    itemCount: _suggestedBuildings.length,
                    itemBuilder: (context, index) {
                      final building = _suggestedBuildings[index];
                      return Card(
                        child: ListTile(
                          leading: const Icon(Icons.business),
                          title: Text('Building $building'),
                          trailing: const Icon(Icons.chevron_right),
                          onTap: () => _selectSuggestedBuilding(building),
                        ),
                      );
                    },
                  ),
                ),
              ],

              // Clear selection button
              if (buildingState.selectedBuildingNumber != null) ...[
                const SizedBox(height: 16),
                TextButton(
                  onPressed: _clearSelection,
                  child: const Text('Clear Selection'),
                ),
              ],

              // Add bottom padding for safe area
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  bool _isValidBuildingNumber(String buildingNumber) {
    if (buildingNumber.isEmpty) return false;

    // Check if it's a valid number
    final number = int.tryParse(buildingNumber);
    if (number == null) return false;

    // Check range (1 to 9999)
    return number >= 1 && number <= 9999;
  }

  Future<void> _selectBuilding() async {
    if (!_formKey.currentState!.validate()) return;

    await ref
        .read(buildingProvider.notifier)
        .setSelectedBuilding(_buildingController.text.trim());

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Building ${_buildingController.text} selected'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop();
    }
  }

  Future<void> _selectSuggestedBuilding(String buildingNumber) async {
    await ref
        .read(buildingProvider.notifier)
        .setSelectedBuilding(buildingNumber);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Building $buildingNumber selected'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop();
    }
  }

  Future<void> _clearSelection() async {
    await ref.read(buildingProvider.notifier).clearBuildingState();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Building selection cleared')),
      );
      Navigator.of(context).pop();
    }
  }
}

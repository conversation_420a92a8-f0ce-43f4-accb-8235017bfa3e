import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/building_provider.dart';

class BuildingSelectionScreen extends StatefulWidget {
  const BuildingSelectionScreen({super.key});

  @override
  State<BuildingSelectionScreen> createState() => _BuildingSelectionScreenState();
}

class _BuildingSelectionScreenState extends State<BuildingSelectionScreen> {
  final _buildingController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  List<String> _suggestedBuildings = [];

  @override
  void initState() {
    super.initState();
    _loadSuggestedBuildings();
  }

  @override
  void dispose() {
    _buildingController.dispose();
    super.dispose();
  }

  Future<void> _loadSuggestedBuildings() async {
    final buildingProvider = Provider.of<BuildingProvider>(context, listen: false);
    final suggestions = await buildingProvider.getSuggestedBuildings();
    setState(() {
      _suggestedBuildings = suggestions;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Building'),
      ),
      body: Consumer<BuildingProvider>(
        builder: (context, buildingProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Current selection display
                  if (buildingProvider.hasBuildingSelected) ...[
                    Card(
                      color: Colors.blue.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Currently Selected',
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                color: Colors.blue.shade700,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Building ${buildingProvider.selectedBuildingNumber}',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Building number input
                  TextFormField(
                    controller: _buildingController,
                    decoration: const InputDecoration(
                      labelText: 'Building Number',
                      hintText: 'Enter building number (1-9999)',
                      prefixIcon: Icon(Icons.business),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a building number';
                      }
                      if (!buildingProvider.isValidBuildingNumber(value)) {
                        return 'Please enter a valid building number (1-9999)';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  // Submit button
                  ElevatedButton(
                    onPressed: buildingProvider.isLoading ? null : _selectBuilding,
                    child: buildingProvider.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Select Building'),
                  ),

                  // Suggested buildings
                  if (_suggestedBuildings.isNotEmpty) ...[
                    const SizedBox(height: 32),
                    Text(
                      'Previously Used Buildings',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _suggestedBuildings.length,
                        itemBuilder: (context, index) {
                          final building = _suggestedBuildings[index];
                          return Card(
                            child: ListTile(
                              leading: const Icon(Icons.business),
                              title: Text('Building $building'),
                              trailing: const Icon(Icons.chevron_right),
                              onTap: () => _selectSuggestedBuilding(building),
                            ),
                          );
                        },
                      ),
                    ),
                  ],

                  // Clear selection button
                  if (buildingProvider.hasBuildingSelected) ...[
                    const SizedBox(height: 16),
                    TextButton(
                      onPressed: _clearSelection,
                      child: const Text('Clear Selection'),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _selectBuilding() async {
    if (!_formKey.currentState!.validate()) return;

    final buildingProvider = Provider.of<BuildingProvider>(context, listen: false);
    await buildingProvider.setSelectedBuilding(_buildingController.text.trim());

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Building ${_buildingController.text} selected'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop();
    }
  }

  Future<void> _selectSuggestedBuilding(String buildingNumber) async {
    final buildingProvider = Provider.of<BuildingProvider>(context, listen: false);
    await buildingProvider.setSelectedBuilding(buildingNumber);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Building $buildingNumber selected'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop();
    }
  }

  Future<void> _clearSelection() async {
    final buildingProvider = Provider.of<BuildingProvider>(context, listen: false);
    await buildingProvider.clearBuildingState();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Building selection cleared'),
        ),
      );
      Navigator.of(context).pop();
    }
  }
}

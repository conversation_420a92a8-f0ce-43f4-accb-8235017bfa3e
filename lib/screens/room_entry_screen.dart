import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/building_provider.dart';
import '../providers/reading_provider.dart';

import 'meter_reading_screen.dart';

class RoomEntryScreen extends StatefulWidget {
  const RoomEntryScreen({super.key});

  @override
  State<RoomEntryScreen> createState() => _RoomEntryScreenState();
}

class _RoomEntryScreenState extends State<RoomEntryScreen> {
  final _roomController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  List<String> _suggestedRooms = [];

  @override
  void initState() {
    super.initState();
    _loadSuggestedRooms();
  }

  @override
  void dispose() {
    _roomController.dispose();
    super.dispose();
  }

  Future<void> _loadSuggestedRooms() async {
    final buildingProvider = Provider.of<BuildingProvider>(
      context,
      listen: false,
    );
    final suggestions = await buildingProvider.getSuggestedRooms();
    setState(() {
      _suggestedRooms = suggestions;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Enter Room')),
      body: Consumer2<BuildingProvider, ReadingProvider>(
        builder: (context, buildingProvider, readingProvider, child) {
          if (!buildingProvider.hasBuildingSelected) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.warning, size: 64, color: Colors.orange),
                  SizedBox(height: 16),
                  Text('No building selected'),
                  Text('Please select a building first'),
                ],
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Building info
                  Card(
                    color: Colors.blue.shade50,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Selected Building',
                            style: Theme.of(
                              context,
                            ).textTheme.titleSmall?.copyWith(
                              color: Colors.blue.shade700,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Building ${buildingProvider.selectedBuildingNumber}',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Current room display
                  if (buildingProvider.hasRoomSelected) ...[
                    Card(
                      color: Colors.green.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Current Room',
                              style: Theme.of(
                                context,
                              ).textTheme.titleSmall?.copyWith(
                                color: Colors.green.shade700,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Room ${buildingProvider.currentRoomNumber}',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 12),
                            _buildRoomProgress(
                              buildingProvider.selectedBuildingNumber!,
                              buildingProvider.currentRoomNumber!,
                              readingProvider,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Room number input
                  TextFormField(
                    controller: _roomController,
                    decoration: const InputDecoration(
                      labelText: 'Room Number',
                      hintText: 'Enter room number (1-9999)',
                      prefixIcon: Icon(Icons.room),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a room number';
                      }
                      if (!buildingProvider.isValidRoomNumber(value)) {
                        return 'Please enter a valid room number (1-9999)';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  // Enter room button
                  ElevatedButton(
                    onPressed: buildingProvider.isLoading ? null : _enterRoom,
                    child:
                        buildingProvider.isLoading
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Text('Enter Room'),
                  ),

                  // Continue with current room button
                  if (buildingProvider.hasRoomSelected) ...[
                    const SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: _continueWithCurrentRoom,
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('Continue with Current Room'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],

                  // Suggested rooms
                  if (_suggestedRooms.isNotEmpty) ...[
                    const SizedBox(height: 32),
                    Text(
                      'Previously Used Rooms',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _suggestedRooms.length,
                        itemBuilder: (context, index) {
                          final room = _suggestedRooms[index];
                          final completion = readingProvider
                              .getRoomCompletionPercentage(
                                buildingProvider.selectedBuildingNumber!,
                                room,
                              );

                          return Card(
                            child: ListTile(
                              leading: const Icon(Icons.room),
                              title: Text('Room $room'),
                              subtitle: LinearProgressIndicator(
                                value: completion,
                                backgroundColor: Colors.grey.shade300,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  completion == 1.0
                                      ? Colors.green
                                      : Colors.blue,
                                ),
                              ),
                              trailing: Text('${(completion * 100).round()}%'),
                              onTap: () => _selectSuggestedRoom(room),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRoomProgress(
    String buildingNumber,
    String roomNumber,
    ReadingProvider readingProvider,
  ) {
    final completion = readingProvider.getRoomCompletionPercentage(
      buildingNumber,
      roomNumber,
    );
    final missingTypes = readingProvider.getMissingMeterTypes(
      buildingNumber,
      roomNumber,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Progress'),
            Text('${(completion * 100).round()}%'),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: completion,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(
            completion == 1.0 ? Colors.green : Colors.blue,
          ),
        ),
        if (missingTypes.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Missing: ${missingTypes.map((t) => t.displayName).join(', ')}',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.orange.shade700),
          ),
        ],
      ],
    );
  }

  Future<void> _enterRoom() async {
    if (!_formKey.currentState!.validate()) return;

    final buildingProvider = Provider.of<BuildingProvider>(
      context,
      listen: false,
    );
    await buildingProvider.setCurrentRoom(_roomController.text.trim());

    if (mounted) {
      _navigateToMeterReading();
    }
  }

  Future<void> _selectSuggestedRoom(String roomNumber) async {
    final buildingProvider = Provider.of<BuildingProvider>(
      context,
      listen: false,
    );
    await buildingProvider.setCurrentRoom(roomNumber);

    if (mounted) {
      _navigateToMeterReading();
    }
  }

  void _continueWithCurrentRoom() {
    _navigateToMeterReading();
  }

  void _navigateToMeterReading() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (_) => const MeterReadingScreen()));
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/building_provider.dart';

import 'meter_reading_screen.dart';

class RoomEntryScreen extends ConsumerStatefulWidget {
  const RoomEntryScreen({super.key});

  @override
  ConsumerState<RoomEntryScreen> createState() => _RoomEntryScreenState();
}

class _RoomEntryScreenState extends ConsumerState<RoomEntryScreen> {
  @override
  Widget build(BuildContext context) {
    final buildingState = ref.watch(buildingProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Enter Room')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.room, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text('Room Entry Screen'),
            const SizedBox(height: 8),
            if (buildingState.selectedBuildingNumber != null)
              Text('Building: ${buildingState.selectedBuildingNumber}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed:
                  () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) => const MeterReadingScreen(),
                    ),
                  ),
              child: const Text('Continue to Meter Reading'),
            ),
          ],
        ),
      ),
    );
  }
}

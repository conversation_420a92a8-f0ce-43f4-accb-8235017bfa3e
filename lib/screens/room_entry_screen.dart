import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/building_provider.dart';
import '../providers/reading_provider.dart';
import '../models/meter_type.dart';
import 'meter_reading_screen.dart';

class RoomEntryScreen extends ConsumerStatefulWidget {
  const RoomEntryScreen({super.key});

  @override
  ConsumerState<RoomEntryScreen> createState() => _RoomEntryScreenState();
}

class _RoomEntryScreenState extends ConsumerState<RoomEntryScreen> {
  final _roomController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  List<String> _suggestedRooms = [];

  @override
  void initState() {
    super.initState();
    _loadSuggestedRooms();
  }

  @override
  void dispose() {
    _roomController.dispose();
    super.dispose();
  }

  Future<void> _loadSuggestedRooms() async {
    final suggestions = await ref.read(buildingProvider.notifier).getSuggestedRooms();
    if (mounted) {
      setState(() {
        _suggestedRooms = suggestions;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final buildingState = ref.watch(buildingProvider);
    final readingState = ref.watch(readingProvider);
    
    if (buildingState.selectedBuildingNumber == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Enter Room')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.warning, size: 64, color: Colors.orange),
              SizedBox(height: 16),
              Text('No building selected'),
              Text('Please select a building first'),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Enter Room'),
        actions: [
          if (buildingState.currentRoomNumber != null)
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearCurrentRoom,
              tooltip: 'Clear current room',
            ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Building info card
              _buildBuildingInfoCard(buildingState),
              const SizedBox(height: 16),

              // Current room progress (if room is selected)
              if (buildingState.currentRoomNumber != null) ...[
                _buildCurrentRoomCard(buildingState, readingState),
                const SizedBox(height: 16),
              ],

              // Room number input
              TextFormField(
                controller: _roomController,
                decoration: const InputDecoration(
                  labelText: 'Room Number',
                  hintText: 'Enter room number (1-9999)',
                  prefixIcon: Icon(Icons.room),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a room number';
                  }
                  if (!_isValidRoomNumber(value)) {
                    return 'Please enter a valid room number (1-9999)';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Action buttons
              _buildActionButtons(buildingState),

              // Suggested rooms
              if (_suggestedRooms.isNotEmpty) ...[
                const SizedBox(height: 32),
                _buildSuggestedRoomsSection(buildingState, readingState),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBuildingInfoCard(buildingState) {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Selected Building',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: Colors.blue.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Building ${buildingState.selectedBuildingNumber}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentRoomCard(buildingState, readingState) {
    final roomReadings = readingState.readings.where((reading) =>
        reading.buildingNumber == buildingState.selectedBuildingNumber &&
        reading.roomNumber == buildingState.currentRoomNumber).toList();
    
    final recordedTypes = roomReadings.map((r) => r.meterType).toSet();
    final completion = recordedTypes.length / MeterType.values.length;
    final missingTypes = MeterType.values.where((type) => !recordedTypes.contains(type)).toList();

    return Card(
      color: completion == 1.0 ? Colors.green.shade50 : Colors.orange.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  completion == 1.0 ? Icons.check_circle : Icons.pending,
                  color: completion == 1.0 ? Colors.green.shade700 : Colors.orange.shade700,
                ),
                const SizedBox(width: 8),
                Text(
                  'Room ${buildingState.currentRoomNumber}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: completion == 1.0 ? Colors.green.shade700 : Colors.orange.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Progress: ${(completion * 100).round()}%'),
                Text('${recordedTypes.length}/${MeterType.values.length} meters'),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: completion,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                completion == 1.0 ? Colors.green : Colors.orange,
              ),
            ),
            if (missingTypes.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Missing: ${missingTypes.map((t) => t.displayName).join(', ')}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.orange.shade700,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(buildingState) {
    return Column(
      children: [
        // Enter room button
        ElevatedButton(
          onPressed: _enterRoom,
          child: const Text('Enter Room'),
        ),
        
        // Continue with current room button (if room is selected)
        if (buildingState.currentRoomNumber != null) ...[
          const SizedBox(height: 12),
          ElevatedButton.icon(
            onPressed: _continueWithCurrentRoom,
            icon: const Icon(Icons.camera_alt),
            label: const Text('Continue with Current Room'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSuggestedRoomsSection(buildingState, readingState) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Previously Used Rooms',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: _suggestedRooms.length,
              itemBuilder: (context, index) {
                final room = _suggestedRooms[index];
                final roomReadings = readingState.readings.where((reading) =>
                    reading.buildingNumber == buildingState.selectedBuildingNumber &&
                    reading.roomNumber == room).toList();
                
                final recordedTypes = roomReadings.map((r) => r.meterType).toSet();
                final completion = recordedTypes.length / MeterType.values.length;
                
                return Card(
                  child: ListTile(
                    leading: Icon(
                      completion == 1.0 ? Icons.check_circle : Icons.room,
                      color: completion == 1.0 ? Colors.green : Colors.blue,
                    ),
                    title: Text('Room $room'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LinearProgressIndicator(
                          value: completion,
                          backgroundColor: Colors.grey.shade300,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            completion == 1.0 ? Colors.green : Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text('${(completion * 100).round()}% complete'),
                      ],
                    ),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _selectSuggestedRoom(room),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  bool _isValidRoomNumber(String roomNumber) {
    if (roomNumber.isEmpty) return false;
    final number = int.tryParse(roomNumber);
    if (number == null) return false;
    return number >= 1 && number <= 9999;
  }

  Future<void> _enterRoom() async {
    if (!_formKey.currentState!.validate()) return;

    await ref.read(buildingProvider.notifier).setCurrentRoom(_roomController.text.trim());
    
    if (mounted) {
      _navigateToMeterReading();
    }
  }

  Future<void> _selectSuggestedRoom(String roomNumber) async {
    await ref.read(buildingProvider.notifier).setCurrentRoom(roomNumber);
    
    if (mounted) {
      _navigateToMeterReading();
    }
  }

  void _continueWithCurrentRoom() {
    _navigateToMeterReading();
  }

  Future<void> _clearCurrentRoom() async {
    await ref.read(buildingProvider.notifier).clearCurrentRoom();
    _roomController.clear();
  }

  void _navigateToMeterReading() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const MeterReadingScreen()),
    );
  }
}

import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;

class ImageUtils {
  // Resize image to fit within max dimensions while maintaining aspect ratio
  static Future<File> resizeImage(
    File imageFile, {
    int maxWidth = 1920,
    int maxHeight = 1080,
  }) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Failed to decode image');
    }

    // Calculate new dimensions
    double aspectRatio = image.width / image.height;
    int newWidth = image.width;
    int newHeight = image.height;

    if (image.width > maxWidth) {
      newWidth = maxWidth;
      newHeight = (newWidth / aspectRatio).round();
    }

    if (newHeight > maxHeight) {
      newHeight = maxHeight;
      newWidth = (newHeight * aspectRatio).round();
    }

    // Only resize if dimensions changed
    if (newWidth != image.width || newHeight != image.height) {
      final resizedImage = img.copyResize(
        image,
        width: newWidth,
        height: newHeight,
      );
      final resizedBytes = img.encodeJpg(resizedImage, quality: 85);
      await imageFile.writeAsBytes(resizedBytes);
    }

    return imageFile;
  }

  // Compress image to reduce file size
  static Future<File> compressImage(File imageFile, {int quality = 85}) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Failed to decode image');
    }

    final compressedBytes = img.encodeJpg(image, quality: quality);
    await imageFile.writeAsBytes(compressedBytes);

    return imageFile;
  }

  // Get image dimensions
  static Future<Size?> getImageDimensions(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image != null) {
        return Size(image.width.toDouble(), image.height.toDouble());
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Check if file is a valid image
  static bool isValidImageFile(File file) {
    final extension = file.path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  // Get file size in bytes
  static Future<int> getFileSize(File file) async {
    try {
      final stat = await file.stat();
      return stat.size;
    } catch (e) {
      return 0;
    }
  }

  // Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // Rotate image by specified degrees
  static Future<File> rotateImage(File imageFile, int degrees) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Failed to decode image');
    }

    img.Image rotatedImage;
    switch (degrees) {
      case 90:
        rotatedImage = img.copyRotate(image, angle: 90);
        break;
      case 180:
        rotatedImage = img.copyRotate(image, angle: 180);
        break;
      case 270:
        rotatedImage = img.copyRotate(image, angle: 270);
        break;
      default:
        rotatedImage = image;
    }

    final rotatedBytes = img.encodeJpg(rotatedImage, quality: 85);
    await imageFile.writeAsBytes(rotatedBytes);

    return imageFile;
  }

  // Create thumbnail from image
  static Future<Uint8List?> createThumbnail(
    File imageFile, {
    int size = 150,
  }) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) return null;

      // Create square thumbnail
      final thumbnail = img.copyResizeCropSquare(image, size: size);
      return Uint8List.fromList(img.encodeJpg(thumbnail, quality: 70));
    } catch (e) {
      return null;
    }
  }

  // Add text watermark to image
  static Future<File> addTextWatermark(
    File imageFile,
    String text, {
    int fontSize = 24,
    Color color = Colors.white,
    double opacity = 0.7,
    Alignment alignment = Alignment.bottomRight,
  }) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Failed to decode image');
    }

    // Calculate position based on alignment
    int x, y;
    switch (alignment) {
      case Alignment.topLeft:
        x = 10;
        y = 10;
        break;
      case Alignment.topRight:
        x = image.width - (text.length * fontSize ~/ 2) - 10;
        y = 10;
        break;
      case Alignment.bottomLeft:
        x = 10;
        y = image.height - fontSize - 10;
        break;
      case Alignment.bottomRight:
      default:
        x = image.width - (text.length * fontSize ~/ 2) - 10;
        y = image.height - fontSize - 10;
        break;
    }

    // Ensure coordinates are within image bounds
    x = x.clamp(0, image.width - 10);
    y = y.clamp(0, image.height - 10);

    // Add text (simplified - using basic font)
    img.drawString(
      image,
      text,
      font: img.arial14,
      x: x,
      y: y,
      color: img.ColorRgba8(
        color.r.round(),
        color.g.round(),
        color.b.round(),
        (255 * opacity).round(),
      ),
    );

    final watermarkedBytes = img.encodeJpg(image, quality: 85);
    await imageFile.writeAsBytes(watermarkedBytes);

    return imageFile;
  }

  // Convert image to grayscale
  static Future<File> convertToGrayscale(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Failed to decode image');
    }

    final grayscaleImage = img.grayscale(image);
    final grayscaleBytes = img.encodeJpg(grayscaleImage, quality: 85);
    await imageFile.writeAsBytes(grayscaleBytes);

    return imageFile;
  }

  // Adjust image brightness
  static Future<File> adjustBrightness(
    File imageFile,
    double brightness,
  ) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Failed to decode image');
    }

    final adjustedImage = img.adjustColor(image, brightness: brightness);
    final adjustedBytes = img.encodeJpg(adjustedImage, quality: 85);
    await imageFile.writeAsBytes(adjustedBytes);

    return imageFile;
  }

  // Adjust image contrast
  static Future<File> adjustContrast(File imageFile, double contrast) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Failed to decode image');
    }

    final adjustedImage = img.adjustColor(image, contrast: contrast);
    final adjustedBytes = img.encodeJpg(adjustedImage, quality: 85);
    await imageFile.writeAsBytes(adjustedBytes);

    return imageFile;
  }
}

import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:intl/intl.dart';

class FileUtils {
  // Get application documents directory
  static Future<Directory> getAppDocumentsDirectory() async {
    return await getApplicationDocumentsDirectory();
  }

  // Get temporary directory
  static Future<Directory> getTempDirectory() async {
    return await getTemporaryDirectory();
  }

  // Create directory if it doesn't exist
  static Future<Directory> ensureDirectoryExists(String directoryPath) async {
    final directory = Directory(directoryPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    return directory;
  }

  // Generate unique filename with timestamp
  static String generateUniqueFileName(String prefix, String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix}_$timestamp.$extension';
  }

  // Generate filename with date
  static String generateDateFileName(String prefix, String extension, {DateTime? date}) {
    final targetDate = date ?? DateTime.now();
    final dateFormat = DateFormat('yyyyMMdd_HHmmss');
    final dateString = dateFormat.format(targetDate);
    return '${prefix}_$dateString.$extension';
  }

  // Get file extension
  static String getFileExtension(String filePath) {
    return path.extension(filePath).toLowerCase();
  }

  // Get filename without extension
  static String getFileNameWithoutExtension(String filePath) {
    return path.basenameWithoutExtension(filePath);
  }

  // Get file size in bytes
  static Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final stat = await file.stat();
        return stat.size;
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  // Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // Check if file exists
  static Future<bool> fileExists(String filePath) async {
    try {
      return await File(filePath).exists();
    } catch (e) {
      return false;
    }
  }

  // Delete file if exists
  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Copy file
  static Future<bool> copyFile(String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);
      if (await sourceFile.exists()) {
        await sourceFile.copy(destinationPath);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Move file
  static Future<bool> moveFile(String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);
      if (await sourceFile.exists()) {
        await sourceFile.rename(destinationPath);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Get files in directory with specific extension
  static Future<List<File>> getFilesWithExtension(
    String directoryPath,
    String extension,
  ) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) return [];

      final files = await directory.list().toList();
      return files
          .whereType<File>()
          .where((file) => file.path.toLowerCase().endsWith(extension.toLowerCase()))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Get all files in directory
  static Future<List<File>> getAllFiles(String directoryPath) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) return [];

      final files = await directory.list().toList();
      return files.whereType<File>().toList();
    } catch (e) {
      return [];
    }
  }

  // Get directory size
  static Future<int> getDirectorySize(String directoryPath) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) return 0;

      int totalSize = 0;
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  // Clean directory (delete all files)
  static Future<int> cleanDirectory(String directoryPath) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) return 0;

      int deletedCount = 0;
      final files = await directory.list().toList();
      
      for (final entity in files) {
        try {
          if (entity is File) {
            await entity.delete();
            deletedCount++;
          }
        } catch (e) {
          // Continue with other files
        }
      }
      
      return deletedCount;
    } catch (e) {
      return 0;
    }
  }

  // Create backup of file
  static Future<String?> createBackup(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return null;

      final directory = path.dirname(filePath);
      final fileName = path.basenameWithoutExtension(filePath);
      final extension = path.extension(filePath);
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      
      final backupPath = path.join(directory, '${fileName}_backup_$timestamp$extension');
      await file.copy(backupPath);
      
      return backupPath;
    } catch (e) {
      return null;
    }
  }

  // Validate file path
  static bool isValidFilePath(String filePath) {
    try {
      // Check for invalid characters
      final invalidChars = ['<', '>', ':', '"', '|', '?', '*'];
      for (final char in invalidChars) {
        if (filePath.contains(char)) return false;
      }
      
      // Check path length (Windows limitation)
      if (filePath.length > 260) return false;
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Sanitize filename
  static String sanitizeFileName(String fileName) {
    // Replace invalid characters with underscore
    final invalidChars = RegExp(r'[<>:"/\\|?*]');
    String sanitized = fileName.replaceAll(invalidChars, '_');
    
    // Remove leading/trailing spaces and dots
    sanitized = sanitized.trim().replaceAll(RegExp(r'^\.+|\.+$'), '');
    
    // Ensure filename is not empty
    if (sanitized.isEmpty) {
      sanitized = 'unnamed_file';
    }
    
    // Limit length
    if (sanitized.length > 100) {
      sanitized = sanitized.substring(0, 100);
    }
    
    return sanitized;
  }

  // Get file modification date
  static Future<DateTime?> getFileModificationDate(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final stat = await file.stat();
        return stat.modified;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Check available storage space
  static Future<int> getAvailableSpace(String directoryPath) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) return 0;

      // This is a simplified implementation
      // In a real app, you might want to use platform-specific code
      // to get actual available space
      return 1024 * 1024 * 1024; // Return 1GB as placeholder
    } catch (e) {
      return 0;
    }
  }

  // Create file with content
  static Future<bool> createFileWithContent(String filePath, String content) async {
    try {
      final file = File(filePath);
      await file.writeAsString(content);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Read file content
  static Future<String?> readFileContent(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsString();
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}

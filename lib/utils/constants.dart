class AppConstants {
  // Database
  static const String databaseName = 'meter_readings.db';
  static const int databaseVersion = 1;
  
  // Tables
  static const String readingsTable = 'readings';
  static const String buildingsTable = 'buildings';
  
  // Meter Types
  static const String meterTypeMain = 'Main';
  static const String meterTypeGenerator = 'Generator';
  static const String meterTypeUtility = 'Utility';
  static const String meterTypeWater = 'Water';
  
  // Units
  static const String unitKwh = 'kWh';
  static const String unitM3 = 'm³';
  
  // File paths
  static const String imagesFolder = 'meter_images';
  static const String exportsFolder = 'exports';
  static const String importsFolder = 'imports';
  
  // Export/Import
  static const String exportFilePrefix = 'meter_readings_';
  static const String exportFileExtension = '.zip';
  static const String dataFileName = 'readings_data.json';
  
  // Image annotation
  static const double annotationFontSize = 16.0;
  static const int annotationBackgroundOpacity = 180;
  
  // UI
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  
  // Validation
  static const int maxBuildingNumber = 9999;
  static const int maxRoomNumber = 9999;
  static const double maxReadingValue = 999999.99;
}

class MeterTypeConfig {
  final String name;
  final String unit;
  final String icon;
  
  const MeterTypeConfig({
    required this.name,
    required this.unit,
    required this.icon,
  });
  
  static const List<MeterTypeConfig> allTypes = [
    MeterTypeConfig(
      name: AppConstants.meterTypeMain,
      unit: AppConstants.unitKwh,
      icon: '⚡',
    ),
    MeterTypeConfig(
      name: AppConstants.meterTypeGenerator,
      unit: AppConstants.unitKwh,
      icon: '🔌',
    ),
    MeterTypeConfig(
      name: AppConstants.meterTypeUtility,
      unit: AppConstants.unitKwh,
      icon: '💡',
    ),
    MeterTypeConfig(
      name: AppConstants.meterTypeWater,
      unit: AppConstants.unitM3,
      icon: '💧',
    ),
  ];
  
  static MeterTypeConfig getByName(String name) {
    return allTypes.firstWhere(
      (type) => type.name == name,
      orElse: () => allTypes.first,
    );
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/database_service.dart';
import '../services/export_service.dart';
import '../services/image_service.dart';

class AppState {
  final bool isInitialized;
  final bool isLoading;
  final String? error;
  final Map<Permission, PermissionStatus> permissions;

  const AppState({
    this.isInitialized = false,
    this.isLoading = false,
    this.error,
    this.permissions = const {},
  });

  AppState copyWith({
    bool? isInitialized,
    bool? isLoading,
    String? error,
    Map<Permission, PermissionStatus>? permissions,
  }) {
    return AppState(
      isInitialized: isInitialized ?? this.isInitialized,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      permissions: permissions ?? this.permissions,
    );
  }

  // Check if camera permission is granted
  bool get hasCameraPermission {
    return permissions[Permission.camera]?.isGranted ?? false;
  }

  // Check if storage permission is granted
  bool get hasStoragePermission {
    return (permissions[Permission.storage]?.isGranted ?? false) ||
           (permissions[Permission.manageExternalStorage]?.isGranted ?? false);
  }

  // Check if all required permissions are granted
  bool get hasAllRequiredPermissions {
    return hasCameraPermission && hasStoragePermission;
  }
}

class AppStateNotifier extends StateNotifier<AppState> {
  final DatabaseService _databaseService = DatabaseService();
  final ExportService _exportService = ExportService();
  final ImageService _imageService = ImageService();

  AppStateNotifier() : super(const AppState());

  // Initialize the application
  Future<void> initialize() async {
    if (state.isInitialized) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      // Check and request permissions
      final permissions = await _checkPermissions();
      
      // Initialize database
      await _databaseService.database;
      
      state = state.copyWith(
        isInitialized: true,
        isLoading: false,
        permissions: permissions,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize app: $e',
      );
    }
  }

  // Check and request necessary permissions
  Future<Map<Permission, PermissionStatus>> _checkPermissions() async {
    final permissionsToCheck = [
      Permission.camera,
      Permission.storage,
      Permission.manageExternalStorage,
    ];

    final permissions = <Permission, PermissionStatus>{};

    for (final permission in permissionsToCheck) {
      final status = await permission.status;
      permissions[permission] = status;
      
      if (status.isDenied) {
        final newStatus = await permission.request();
        permissions[permission] = newStatus;
      }
    }

    return permissions;
  }

  // Request specific permission
  Future<bool> requestPermission(Permission permission) async {
    try {
      final status = await permission.request();
      final updatedPermissions = Map<Permission, PermissionStatus>.from(state.permissions);
      updatedPermissions[permission] = status;
      
      state = state.copyWith(permissions: updatedPermissions);
      return status.isGranted;
    } catch (e) {
      state = state.copyWith(error: 'Failed to request permission: $e');
      return false;
    }
  }

  // Open app settings for permission management
  Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      state = state.copyWith(error: 'Failed to open app settings: $e');
    }
  }

  // Get app statistics
  Future<Map<String, dynamic>> getAppStatistics() async {
    try {
      final readingsCount = await _databaseService.getReadingsCount();
      final buildings = await _databaseService.getDistinctBuildings();
      final imageFiles = await _imageService.getAllImageFiles();
      
      return {
        'totalReadings': readingsCount,
        'totalBuildings': buildings.length,
        'totalImages': imageFiles.length,
        'databaseSize': 0, // Could be calculated if needed
        'lastUpdated': DateTime.now(),
      };
    } catch (e) {
      state = state.copyWith(error: 'Failed to get app statistics: $e');
      return {};
    }
  }

  // Cleanup orphaned data
  Future<Map<String, int>> cleanupOrphanedData() async {
    state = state.copyWith(isLoading: true);

    try {
      final allReadings = await _databaseService.getAllReadings();
      
      // Cleanup orphaned images
      final deletedImages = await _imageService.cleanupOrphanedImages(allReadings);
      
      // Cleanup old exports
      final deletedExports = await _exportService.cleanupOldExports(keepCount: 5);
      
      final result = {
        'deletedImages': deletedImages,
        'deletedExports': deletedExports,
      };
      
      state = state.copyWith(isLoading: false);
      return result;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to cleanup orphaned data: $e',
      );
      return {'deletedImages': 0, 'deletedExports': 0};
    }
  }

  // Reset application data
  Future<bool> resetApplicationData() async {
    state = state.copyWith(isLoading: true);

    try {
      // Get all readings to clean up images
      final allReadings = await _databaseService.getAllReadings();
      
      // Delete all images
      for (final reading in allReadings) {
        if (reading.imagePath.isNotEmpty) {
          await _imageService.deleteImage(reading.imagePath);
        }
      }
      
      // Close and delete database
      await _databaseService.close();
      // Note: In a real implementation, you'd need to delete the database file
      
      // Clear building state
      await _databaseService.clearBuildingState();
      
      // Reinitialize
      await initialize();
      
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to reset application data: $e',
      );
      return false;
    }
  }

  // Check app health
  Future<Map<String, dynamic>> checkAppHealth() async {
    final health = <String, dynamic>{
      'isHealthy': true,
      'issues': <String>[],
      'warnings': <String>[],
    };

    try {
      // Check database connectivity
      try {
        await _databaseService.getReadingsCount();
      } catch (e) {
        health['isHealthy'] = false;
        health['issues'].add('Database connectivity issue: $e');
      }

      // Check permissions
      if (!state.hasAllRequiredPermissions) {
        health['warnings'].add('Some permissions are not granted');
      }

      // Check storage space (simplified)
      try {
        final imageFiles = await _imageService.getAllImageFiles();
        if (imageFiles.length > 1000) {
          health['warnings'].add('Large number of images stored (${imageFiles.length})');
        }
      } catch (e) {
        health['warnings'].add('Could not check image storage: $e');
      }

    } catch (e) {
      health['isHealthy'] = false;
      health['issues'].add('Health check failed: $e');
    }

    return health;
  }

  // Clear error
  void clearError() {
    if (state.error != null) {
      state = state.copyWith(error: null);
    }
  }
}

// Provider for the app state
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  return AppStateNotifier();
});

import 'package:flutter/foundation.dart';

import 'package:permission_handler/permission_handler.dart';
import '../services/database_service.dart';
import '../services/export_service.dart';

import '../services/image_service.dart';

class AppStateProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final ExportService _exportService = ExportService();
  final ImageService _imageService = ImageService();

  bool _isInitialized = false;
  bool _isLoading = false;
  String? _error;
  final Map<Permission, PermissionStatus> _permissions = {};

  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<Permission, PermissionStatus> get permissions => _permissions;

  // Initialize the application
  Future<void> initialize() async {
    if (_isInitialized) return;

    _setLoading(true);
    _clearError();

    try {
      // Check and request permissions
      await _checkPermissions();

      // Initialize database
      await _databaseService.database;

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      _setError('Failed to initialize app: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Check and request necessary permissions
  Future<void> _checkPermissions() async {
    final permissionsToCheck = [
      Permission.camera,
      Permission.storage,
      Permission.manageExternalStorage,
    ];

    for (final permission in permissionsToCheck) {
      final status = await permission.status;
      _permissions[permission] = status;

      if (status.isDenied) {
        final newStatus = await permission.request();
        _permissions[permission] = newStatus;
      }
    }

    notifyListeners();
  }

  // Check if camera permission is granted
  bool get hasCameraPermission {
    return _permissions[Permission.camera]?.isGranted ?? false;
  }

  // Check if storage permission is granted
  bool get hasStoragePermission {
    return (_permissions[Permission.storage]?.isGranted ?? false) ||
        (_permissions[Permission.manageExternalStorage]?.isGranted ?? false);
  }

  // Check if all required permissions are granted
  bool get hasAllRequiredPermissions {
    return hasCameraPermission && hasStoragePermission;
  }

  // Request specific permission
  Future<bool> requestPermission(Permission permission) async {
    try {
      final status = await permission.request();
      _permissions[permission] = status;
      notifyListeners();
      return status.isGranted;
    } catch (e) {
      _setError('Failed to request permission: $e');
      return false;
    }
  }

  // Open app settings for permission management
  Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      _setError('Failed to open app settings: $e');
    }
  }

  // Get app statistics
  Future<Map<String, dynamic>> getAppStatistics() async {
    try {
      final readingsCount = await _databaseService.getReadingsCount();
      final buildings = await _databaseService.getDistinctBuildings();
      final imageFiles = await _imageService.getAllImageFiles();

      return {
        'totalReadings': readingsCount,
        'totalBuildings': buildings.length,
        'totalImages': imageFiles.length,
        'databaseSize': 0, // Could be calculated if needed
        'lastUpdated': DateTime.now(),
      };
    } catch (e) {
      _setError('Failed to get app statistics: $e');
      return {};
    }
  }

  // Cleanup orphaned data
  Future<Map<String, int>> cleanupOrphanedData() async {
    _setLoading(true);
    _clearError();

    try {
      final allReadings = await _databaseService.getAllReadings();

      // Cleanup orphaned images
      final deletedImages = await _imageService.cleanupOrphanedImages(
        allReadings,
      );

      // Cleanup old exports
      final deletedExports = await _exportService.cleanupOldExports(
        keepCount: 5,
      );

      final result = {
        'deletedImages': deletedImages,
        'deletedExports': deletedExports,
      };

      notifyListeners();
      return result;
    } catch (e) {
      _setError('Failed to cleanup orphaned data: $e');
      return {'deletedImages': 0, 'deletedExports': 0};
    } finally {
      _setLoading(false);
    }
  }

  // Reset application data
  Future<bool> resetApplicationData() async {
    _setLoading(true);
    _clearError();

    try {
      // Get all readings to clean up images
      final allReadings = await _databaseService.getAllReadings();

      // Delete all images
      for (final reading in allReadings) {
        if (reading.imagePath.isNotEmpty) {
          await _imageService.deleteImage(reading.imagePath);
        }
      }

      // Close and delete database
      await _databaseService.close();
      // Note: In a real implementation, you'd need to delete the database file

      // Clear building state
      await _databaseService.clearBuildingState();

      // Reinitialize
      await initialize();

      return true;
    } catch (e) {
      _setError('Failed to reset application data: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Check app health
  Future<Map<String, dynamic>> checkAppHealth() async {
    final health = <String, dynamic>{
      'isHealthy': true,
      'issues': <String>[],
      'warnings': <String>[],
    };

    try {
      // Check database connectivity
      try {
        await _databaseService.getReadingsCount();
      } catch (e) {
        health['isHealthy'] = false;
        health['issues'].add('Database connectivity issue: $e');
      }

      // Check permissions
      if (!hasAllRequiredPermissions) {
        health['warnings'].add('Some permissions are not granted');
      }

      // Check storage space (simplified)
      try {
        final imageFiles = await _imageService.getAllImageFiles();
        if (imageFiles.length > 1000) {
          health['warnings'].add(
            'Large number of images stored (${imageFiles.length})',
          );
        }
      } catch (e) {
        health['warnings'].add('Could not check image storage: $e');
      }
    } catch (e) {
      health['isHealthy'] = false;
      health['issues'].add('Health check failed: $e');
    }

    return health;
  }

  // Set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  // Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  // Clear error message
  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/meter_reading.dart';
import '../models/meter_type.dart';
import '../services/database_service.dart';
import '../services/image_service.dart';

class ReadingState {
  final List<MeterReading> readings;
  final bool isLoading;
  final String? error;

  const ReadingState({
    this.readings = const [],
    this.isLoading = false,
    this.error,
  });

  ReadingState copyWith({
    List<MeterReading>? readings,
    bool? isLoading,
    String? error,
  }) {
    return ReadingState(
      readings: readings ?? this.readings,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class ReadingNotifier extends StateNotifier<ReadingState> {
  final DatabaseService _databaseService = DatabaseService();
  final ImageService _imageService = ImageService();

  ReadingNotifier() : super(const ReadingState());

  // Initialize provider
  Future<void> initialize() async {
    await loadAllReadings();
  }

  // Load all readings
  Future<void> loadAllReadings() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final readings = await _databaseService.getAllReadings();
      state = state.copyWith(readings: readings, isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load readings: $e',
      );
    }
  }

  // Load readings by building
  Future<void> loadReadingsByBuilding(String buildingNumber) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final readings = await _databaseService.getReadingsByBuilding(
        buildingNumber,
      );
      state = state.copyWith(readings: readings, isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load readings for building $buildingNumber: $e',
      );
    }
  }

  // Load readings by room
  Future<void> loadReadingsByRoom(
    String buildingNumber,
    String roomNumber,
  ) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final readings = await _databaseService.getReadingsByRoom(
        buildingNumber,
        roomNumber,
      );
      state = state.copyWith(readings: readings, isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error:
            'Failed to load readings for room $buildingNumber-$roomNumber: $e',
      );
    }
  }

  // Load readings by date range
  Future<void> loadReadingsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final readings = await _databaseService.getReadingsByDateRange(
        startDate,
        endDate,
      );
      state = state.copyWith(readings: readings, isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load readings for date range: $e',
      );
    }
  }

  // Add new reading
  Future<bool> addReading(MeterReading reading) async {
    try {
      final id = await _databaseService.insertReading(reading);

      // Add to local list with the new ID
      final newReading = reading.copyWith(id: id);
      final updatedReadings = [newReading, ...state.readings];
      state = state.copyWith(readings: updatedReadings);

      return true;
    } catch (e) {
      state = state.copyWith(error: 'Failed to add reading: $e');
      return false;
    }
  }

  // Update existing reading
  Future<bool> updateReading(MeterReading reading) async {
    try {
      await _databaseService.updateReading(reading);

      // Update in local list
      final updatedReadings =
          state.readings.map((r) {
            return r.id == reading.id ? reading : r;
          }).toList();

      state = state.copyWith(readings: updatedReadings);
      return true;
    } catch (e) {
      state = state.copyWith(error: 'Failed to update reading: $e');
      return false;
    }
  }

  // Delete reading
  Future<bool> deleteReading(int readingId) async {
    try {
      // Find the reading to get image path
      final reading = state.readings.firstWhere((r) => r.id == readingId);

      // Delete from database
      await _databaseService.deleteReading(readingId);

      // Delete associated image
      if (reading.imagePath.isNotEmpty) {
        await _imageService.deleteImage(reading.imagePath);
      }

      // Remove from local list
      final updatedReadings =
          state.readings.where((r) => r.id != readingId).toList();
      state = state.copyWith(readings: updatedReadings);

      return true;
    } catch (e) {
      state = state.copyWith(error: 'Failed to delete reading: $e');
      return false;
    }
  }

  // Get readings for specific room and check completion
  List<MeterReading> getReadingsForRoom(
    String buildingNumber,
    String roomNumber,
  ) {
    return state.readings
        .where(
          (reading) =>
              reading.buildingNumber == buildingNumber &&
              reading.roomNumber == roomNumber,
        )
        .toList();
  }

  // Check if all meter types are recorded for a room
  bool isRoomComplete(String buildingNumber, String roomNumber) {
    final roomReadings = getReadingsForRoom(buildingNumber, roomNumber);
    final recordedTypes = roomReadings.map((r) => r.meterType).toSet();

    return recordedTypes.length == MeterType.values.length;
  }

  // Get missing meter types for a room
  List<MeterType> getMissingMeterTypes(
    String buildingNumber,
    String roomNumber,
  ) {
    final roomReadings = getReadingsForRoom(buildingNumber, roomNumber);
    final recordedTypes = roomReadings.map((r) => r.meterType).toSet();

    return MeterType.values
        .where((type) => !recordedTypes.contains(type))
        .toList();
  }

  // Get next meter type to record for a room
  MeterType? getNextMeterType(String buildingNumber, String roomNumber) {
    final missingTypes = getMissingMeterTypes(buildingNumber, roomNumber);
    return missingTypes.isNotEmpty ? missingTypes.first : null;
  }

  // Get room completion percentage
  double getRoomCompletionPercentage(String buildingNumber, String roomNumber) {
    final roomReadings = getReadingsForRoom(buildingNumber, roomNumber);
    final recordedTypes = roomReadings.map((r) => r.meterType).toSet();

    return recordedTypes.length / MeterType.values.length;
  }

  // Get statistics
  Map<String, dynamic> getStatistics() {
    final totalReadings = state.readings.length;
    final buildings = state.readings.map((r) => r.buildingNumber).toSet();
    final rooms =
        state.readings
            .map((r) => '${r.buildingNumber}-${r.roomNumber}')
            .toSet();

    final meterTypeCounts = <MeterType, int>{};
    for (final type in MeterType.values) {
      meterTypeCounts[type] =
          state.readings.where((r) => r.meterType == type).length;
    }

    DateTime? earliestReading;
    DateTime? latestReading;

    for (final reading in state.readings) {
      if (earliestReading == null ||
          reading.timestamp.isBefore(earliestReading)) {
        earliestReading = reading.timestamp;
      }
      if (latestReading == null || reading.timestamp.isAfter(latestReading)) {
        latestReading = reading.timestamp;
      }
    }

    return {
      'totalReadings': totalReadings,
      'totalBuildings': buildings.length,
      'totalRooms': rooms.length,
      'meterTypeCounts': meterTypeCounts,
      'earliestReading': earliestReading,
      'latestReading': latestReading,
      'buildings': buildings.toList()..sort(),
    };
  }

  // Search readings
  List<MeterReading> searchReadings(String query) {
    if (query.isEmpty) return state.readings;

    final lowerQuery = query.toLowerCase();
    return state.readings
        .where(
          (reading) =>
              reading.buildingNumber.toLowerCase().contains(lowerQuery) ||
              reading.roomNumber.toLowerCase().contains(lowerQuery) ||
              reading.meterType.displayName.toLowerCase().contains(
                lowerQuery,
              ) ||
              reading.readingValue.toString().contains(lowerQuery),
        )
        .toList();
  }

  // Filter readings by meter type
  List<MeterReading> filterByMeterType(MeterType meterType) {
    return state.readings
        .where((reading) => reading.meterType == meterType)
        .toList();
  }

  // Group readings by building
  Map<String, List<MeterReading>> groupByBuilding() {
    final grouped = <String, List<MeterReading>>{};

    for (final reading in state.readings) {
      grouped.putIfAbsent(reading.buildingNumber, () => []).add(reading);
    }

    return grouped;
  }

  // Group readings by room
  Map<String, List<MeterReading>> groupByRoom() {
    final grouped = <String, List<MeterReading>>{};

    for (final reading in state.readings) {
      final roomKey = '${reading.buildingNumber}-${reading.roomNumber}';
      grouped.putIfAbsent(roomKey, () => []).add(reading);
    }

    return grouped;
  }

  // Clear all readings (for testing or reset)
  void clearReadings() {
    state = state.copyWith(readings: []);
  }

  // Load readings (alias for loadAllReadings)
  Future<void> loadReadings() async {
    await loadAllReadings();
  }

  // Refresh readings from database
  Future<void> refresh() async {
    await loadAllReadings();
  }

  // Clear error
  void clearError() {
    if (state.error != null) {
      state = state.copyWith(error: null);
    }
  }
}

// Provider for the reading state
final readingProvider = StateNotifierProvider<ReadingNotifier, ReadingState>((
  ref,
) {
  return ReadingNotifier();
});

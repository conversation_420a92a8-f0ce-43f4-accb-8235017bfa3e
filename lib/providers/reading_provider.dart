import 'package:flutter/foundation.dart';
import '../models/meter_reading.dart';
import '../models/meter_type.dart';
import '../services/database_service.dart';
import '../services/image_service.dart';

class ReadingProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final ImageService _imageService = ImageService();

  List<MeterReading> _readings = [];
  bool _isLoading = false;
  String? _error;

  List<MeterReading> get readings => _readings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize provider
  Future<void> initialize() async {
    await loadAllReadings();
  }

  // Load all readings
  Future<void> loadAllReadings() async {
    _setLoading(true);
    _clearError();

    try {
      _readings = await _databaseService.getAllReadings();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load readings: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load readings by building
  Future<void> loadReadingsByBuilding(String buildingNumber) async {
    _setLoading(true);
    _clearError();

    try {
      _readings = await _databaseService.getReadingsByBuilding(buildingNumber);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load readings for building $buildingNumber: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load readings by room
  Future<void> loadReadingsByRoom(
    String buildingNumber,
    String roomNumber,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      _readings = await _databaseService.getReadingsByRoom(
        buildingNumber,
        roomNumber,
      );
      notifyListeners();
    } catch (e) {
      _setError(
        'Failed to load readings for room $buildingNumber-$roomNumber: $e',
      );
    } finally {
      _setLoading(false);
    }
  }

  // Load readings by date range
  Future<void> loadReadingsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      _readings = await _databaseService.getReadingsByDateRange(
        startDate,
        endDate,
      );
      notifyListeners();
    } catch (e) {
      _setError('Failed to load readings for date range: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add new reading
  Future<bool> addReading(MeterReading reading) async {
    _clearError();

    try {
      final id = await _databaseService.insertReading(reading);

      // Add to local list with the new ID
      final newReading = reading.copyWith(id: id);
      _readings.insert(0, newReading); // Insert at beginning for newest first
      notifyListeners();

      return true;
    } catch (e) {
      _setError('Failed to add reading: $e');
      return false;
    }
  }

  // Update existing reading
  Future<bool> updateReading(MeterReading reading) async {
    _clearError();

    try {
      await _databaseService.updateReading(reading);

      // Update in local list
      final index = _readings.indexWhere((r) => r.id == reading.id);
      if (index != -1) {
        _readings[index] = reading;
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('Failed to update reading: $e');
      return false;
    }
  }

  // Delete reading
  Future<bool> deleteReading(int readingId) async {
    _clearError();

    try {
      // Find the reading to get image path
      final reading = _readings.firstWhere((r) => r.id == readingId);

      // Delete from database
      await _databaseService.deleteReading(readingId);

      // Delete associated image
      if (reading.imagePath.isNotEmpty) {
        await _imageService.deleteImage(reading.imagePath);
      }

      // Remove from local list
      _readings.removeWhere((r) => r.id == readingId);
      notifyListeners();

      return true;
    } catch (e) {
      _setError('Failed to delete reading: $e');
      return false;
    }
  }

  // Get readings for specific room and check completion
  List<MeterReading> getReadingsForRoom(
    String buildingNumber,
    String roomNumber,
  ) {
    return _readings
        .where(
          (reading) =>
              reading.buildingNumber == buildingNumber &&
              reading.roomNumber == roomNumber,
        )
        .toList();
  }

  // Check if all meter types are recorded for a room
  bool isRoomComplete(String buildingNumber, String roomNumber) {
    final roomReadings = getReadingsForRoom(buildingNumber, roomNumber);
    final recordedTypes = roomReadings.map((r) => r.meterType).toSet();

    return recordedTypes.length == MeterType.values.length;
  }

  // Get missing meter types for a room
  List<MeterType> getMissingMeterTypes(
    String buildingNumber,
    String roomNumber,
  ) {
    final roomReadings = getReadingsForRoom(buildingNumber, roomNumber);
    final recordedTypes = roomReadings.map((r) => r.meterType).toSet();

    return MeterType.values
        .where((type) => !recordedTypes.contains(type))
        .toList();
  }

  // Get next meter type to record for a room
  MeterType? getNextMeterType(String buildingNumber, String roomNumber) {
    final missingTypes = getMissingMeterTypes(buildingNumber, roomNumber);
    return missingTypes.isNotEmpty ? missingTypes.first : null;
  }

  // Get room completion percentage
  double getRoomCompletionPercentage(String buildingNumber, String roomNumber) {
    final roomReadings = getReadingsForRoom(buildingNumber, roomNumber);
    final recordedTypes = roomReadings.map((r) => r.meterType).toSet();

    return recordedTypes.length / MeterType.values.length;
  }

  // Get statistics
  Map<String, dynamic> getStatistics() {
    final totalReadings = _readings.length;
    final buildings = _readings.map((r) => r.buildingNumber).toSet();
    final rooms =
        _readings.map((r) => '${r.buildingNumber}-${r.roomNumber}').toSet();

    final meterTypeCounts = <MeterType, int>{};
    for (final type in MeterType.values) {
      meterTypeCounts[type] =
          _readings.where((r) => r.meterType == type).length;
    }

    DateTime? earliestReading;
    DateTime? latestReading;

    for (final reading in _readings) {
      if (earliestReading == null ||
          reading.timestamp.isBefore(earliestReading)) {
        earliestReading = reading.timestamp;
      }
      if (latestReading == null || reading.timestamp.isAfter(latestReading)) {
        latestReading = reading.timestamp;
      }
    }

    return {
      'totalReadings': totalReadings,
      'totalBuildings': buildings.length,
      'totalRooms': rooms.length,
      'meterTypeCounts': meterTypeCounts,
      'earliestReading': earliestReading,
      'latestReading': latestReading,
      'buildings': buildings.toList()..sort(),
    };
  }

  // Search readings
  List<MeterReading> searchReadings(String query) {
    if (query.isEmpty) return _readings;

    final lowerQuery = query.toLowerCase();
    return _readings
        .where(
          (reading) =>
              reading.buildingNumber.toLowerCase().contains(lowerQuery) ||
              reading.roomNumber.toLowerCase().contains(lowerQuery) ||
              reading.meterType.displayName.toLowerCase().contains(
                lowerQuery,
              ) ||
              reading.readingValue.toString().contains(lowerQuery),
        )
        .toList();
  }

  // Filter readings by meter type
  List<MeterReading> filterByMeterType(MeterType meterType) {
    return _readings
        .where((reading) => reading.meterType == meterType)
        .toList();
  }

  // Group readings by building
  Map<String, List<MeterReading>> groupByBuilding() {
    final grouped = <String, List<MeterReading>>{};

    for (final reading in _readings) {
      grouped.putIfAbsent(reading.buildingNumber, () => []).add(reading);
    }

    return grouped;
  }

  // Group readings by room
  Map<String, List<MeterReading>> groupByRoom() {
    final grouped = <String, List<MeterReading>>{};

    for (final reading in _readings) {
      final roomKey = '${reading.buildingNumber}-${reading.roomNumber}';
      grouped.putIfAbsent(roomKey, () => []).add(reading);
    }

    return grouped;
  }

  // Clear all readings (for testing or reset)
  void clearReadings() {
    _readings.clear();
    notifyListeners();
  }

  // Refresh readings from database
  Future<void> refresh() async {
    await loadAllReadings();
  }

  // Set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  // Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  // Clear error message
  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }
}

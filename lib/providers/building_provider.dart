import 'package:flutter/foundation.dart';
import '../models/building_state.dart';
import '../services/database_service.dart';

class BuildingProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();

  BuildingState _buildingState = BuildingState();
  bool _isLoading = false;

  BuildingState get buildingState => _buildingState;
  bool get isLoading => _isLoading;

  String? get selectedBuildingNumber => _buildingState.selectedBuildingNumber;
  String? get currentRoomNumber => _buildingState.currentRoomNumber;
  DateTime? get lastAccessTime => _buildingState.lastAccessTime;

  // Initialize provider by loading saved state
  Future<void> initialize() async {
    _setLoading(true);
    try {
      final savedState = await _databaseService.getBuildingState();
      if (savedState != null) {
        _buildingState = savedState;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to load building state: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Set selected building number
  Future<void> setSelectedBuilding(String buildingNumber) async {
    try {
      _buildingState = _buildingState.copyWith(
        selectedBuildingNumber: buildingNumber,
        currentRoomNumber: null, // Clear room when building changes
        lastAccessTime: DateTime.now(),
      );

      await _saveBuildingState();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to set selected building: $e');
    }
  }

  // Set current room number
  Future<void> setCurrentRoom(String roomNumber) async {
    try {
      _buildingState = _buildingState.copyWith(
        currentRoomNumber: roomNumber,
        lastAccessTime: DateTime.now(),
      );

      await _saveBuildingState();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to set current room: $e');
    }
  }

  // Clear current room but keep building
  Future<void> clearCurrentRoom() async {
    try {
      _buildingState = _buildingState.copyWith(
        currentRoomNumber: null,
        lastAccessTime: DateTime.now(),
      );

      await _saveBuildingState();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to clear current room: $e');
    }
  }

  // Clear all building state
  Future<void> clearBuildingState() async {
    try {
      _buildingState = BuildingState();
      await _databaseService.clearBuildingState();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to clear building state: $e');
    }
  }

  // Update last access time
  Future<void> updateLastAccessTime() async {
    try {
      _buildingState = _buildingState.copyWith(lastAccessTime: DateTime.now());

      await _saveBuildingState();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to update last access time: $e');
    }
  }

  // Check if building is selected
  bool get hasBuildingSelected => _buildingState.selectedBuildingNumber != null;

  // Check if room is selected
  bool get hasRoomSelected => _buildingState.currentRoomNumber != null;

  // Check if state is complete (both building and room selected)
  bool get isStateComplete => hasBuildingSelected && hasRoomSelected;

  // Get formatted display text for current state
  String get currentStateDisplay {
    if (!hasBuildingSelected) return 'No building selected';
    if (!hasRoomSelected) {
      return 'Building ${_buildingState.selectedBuildingNumber}';
    }
    return 'Building ${_buildingState.selectedBuildingNumber}, Room ${_buildingState.currentRoomNumber}';
  }

  // Save building state to database
  Future<void> _saveBuildingState() async {
    try {
      await _databaseService.saveBuildingState(_buildingState);
    } catch (e) {
      debugPrint('Failed to save building state: $e');
      rethrow;
    }
  }

  // Set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  // Validate building number format
  bool isValidBuildingNumber(String buildingNumber) {
    if (buildingNumber.isEmpty) return false;

    // Check if it's a valid number
    final number = int.tryParse(buildingNumber);
    if (number == null) return false;

    // Check range (1 to 9999)
    return number >= 1 && number <= 9999;
  }

  // Validate room number format
  bool isValidRoomNumber(String roomNumber) {
    if (roomNumber.isEmpty) return false;

    // Check if it's a valid number
    final number = int.tryParse(roomNumber);
    if (number == null) return false;

    // Check range (1 to 9999)
    return number >= 1 && number <= 9999;
  }

  // Get suggested building numbers from database
  Future<List<String>> getSuggestedBuildings() async {
    try {
      return await _databaseService.getDistinctBuildings();
    } catch (e) {
      debugPrint('Failed to get suggested buildings: $e');
      return [];
    }
  }

  // Get suggested room numbers for current building
  Future<List<String>> getSuggestedRooms() async {
    try {
      if (!hasBuildingSelected) return [];
      return await _databaseService.getDistinctRooms(
        _buildingState.selectedBuildingNumber!,
      );
    } catch (e) {
      debugPrint('Failed to get suggested rooms: $e');
      return [];
    }
  }
}

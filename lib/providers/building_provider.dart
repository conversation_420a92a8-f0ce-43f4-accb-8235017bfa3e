import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/building_state.dart';
import '../services/database_service.dart';

class BuildingNotifier extends StateNotifier<BuildingState> {
  final DatabaseService _databaseService = DatabaseService();

  BuildingNotifier() : super(BuildingState());

  // Initialize provider by loading saved state
  Future<void> initialize() async {
    try {
      final savedState = await _databaseService.getBuildingState();
      if (savedState != null) {
        state = savedState;
      }
    } catch (e) {
      // Handle error silently for now
    }
  }

  // Set selected building number
  Future<void> setSelectedBuilding(String buildingNumber) async {
    try {
      state = state.copyWith(
        selectedBuildingNumber: buildingNumber,
        currentRoomNumber: null, // Clear room when building changes
        lastAccessTime: DateTime.now(),
      );

      await _saveBuildingState();
    } catch (e) {
      // Handle error
    }
  }

  // Set current room number
  Future<void> setCurrentRoom(String roomNumber) async {
    try {
      state = state.copyWith(
        currentRoomNumber: roomNumber,
        lastAccessTime: DateTime.now(),
      );

      await _saveBuildingState();
    } catch (e) {
      // Handle error
    }
  }

  // Clear current room but keep building
  Future<void> clearCurrentRoom() async {
    try {
      state = state.copyWith(
        currentRoomNumber: null,
        lastAccessTime: DateTime.now(),
      );

      await _saveBuildingState();
    } catch (e) {
      // Handle error
    }
  }

  // Clear all building state
  Future<void> clearBuildingState() async {
    try {
      state = BuildingState();
      await _databaseService.clearBuildingState();
    } catch (e) {
      // Handle error
    }
  }

  // Update last access time
  Future<void> updateLastAccessTime() async {
    try {
      state = state.copyWith(lastAccessTime: DateTime.now());
      await _saveBuildingState();
    } catch (e) {
      // Handle error
    }
  }

  // Check if building is selected
  bool get hasBuildingSelected => state.selectedBuildingNumber != null;

  // Check if room is selected
  bool get hasRoomSelected => state.currentRoomNumber != null;

  // Check if state is complete (both building and room selected)
  bool get isStateComplete => hasBuildingSelected && hasRoomSelected;

  // Get formatted display text for current state
  String get currentStateDisplay {
    if (!hasBuildingSelected) return 'No building selected';
    if (!hasRoomSelected) {
      return 'Building ${state.selectedBuildingNumber}';
    }
    return 'Building ${state.selectedBuildingNumber}, Room ${state.currentRoomNumber}';
  }

  // Save building state to database
  Future<void> _saveBuildingState() async {
    try {
      await _databaseService.saveBuildingState(state);
    } catch (e) {
      rethrow;
    }
  }

  // Validate building number format
  bool isValidBuildingNumber(String buildingNumber) {
    if (buildingNumber.isEmpty) return false;

    // Check if it's a valid number
    final number = int.tryParse(buildingNumber);
    if (number == null) return false;

    // Check range (1 to 9999)
    return number >= 1 && number <= 9999;
  }

  // Validate room number format
  bool isValidRoomNumber(String roomNumber) {
    if (roomNumber.isEmpty) return false;

    // Check if it's a valid number
    final number = int.tryParse(roomNumber);
    if (number == null) return false;

    // Check range (1 to 9999)
    return number >= 1 && number <= 9999;
  }

  // Get suggested building numbers from database
  Future<List<String>> getSuggestedBuildings() async {
    try {
      return await _databaseService.getDistinctBuildings();
    } catch (e) {
      return [];
    }
  }

  // Get suggested room numbers for current building
  Future<List<String>> getSuggestedRooms() async {
    try {
      if (!hasBuildingSelected) return [];
      return await _databaseService.getDistinctRooms(
        state.selectedBuildingNumber!,
      );
    } catch (e) {
      return [];
    }
  }
}

// Provider for the building state
final buildingProvider = StateNotifierProvider<BuildingNotifier, BuildingState>(
  (ref) {
    return BuildingNotifier();
  },
);

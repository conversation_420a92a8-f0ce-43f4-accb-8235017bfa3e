import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/meter_reading.dart';
import '../models/meter_type.dart';
import '../utils/constants.dart';

class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  // Get the directory for storing meter images
  Future<Directory> get _imagesDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final imagesDir = Directory(
      path.join(appDir.path, AppConstants.imagesFolder),
    );

    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }

    return imagesDir;
  }

  // Generate unique filename for meter image
  String _generateImageFileName(
    String buildingNumber,
    String roomNumber,
    MeterType meterType,
  ) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'meter_${buildingNumber}_${roomNumber}_${meterType.displayName}_$timestamp.jpg';
  }

  // Save image with annotation overlay
  Future<String> saveAnnotatedImage(
    File originalImage,
    String buildingNumber,
    String roomNumber,
    MeterType meterType,
    double readingValue,
  ) async {
    try {
      final imagesDir = await _imagesDirectory;
      final fileName = _generateImageFileName(
        buildingNumber,
        roomNumber,
        meterType,
      );
      final targetPath = path.join(imagesDir.path, fileName);

      // Read original image
      final originalBytes = await originalImage.readAsBytes();
      final originalImg = img.decodeImage(originalBytes);

      if (originalImg == null) {
        throw Exception('Failed to decode image');
      }

      // Create annotated image
      final annotatedImg = await _addAnnotationToImage(
        originalImg,
        buildingNumber,
        roomNumber,
        meterType,
        readingValue,
      );

      // Save annotated image
      final annotatedBytes = img.encodeJpg(annotatedImg, quality: 85);
      final targetFile = File(targetPath);
      await targetFile.writeAsBytes(annotatedBytes);

      return targetPath;
    } catch (e) {
      throw Exception('Failed to save annotated image: $e');
    }
  }

  // Add annotation overlay to image
  Future<img.Image> _addAnnotationToImage(
    img.Image originalImage,
    String buildingNumber,
    String roomNumber,
    MeterType meterType,
    double readingValue,
  ) async {
    // Create a copy of the original image
    final annotatedImage = img.copyResize(
      originalImage,
      width: originalImage.width,
      height: originalImage.height,
    );

    // Prepare annotation text
    final timestamp = DateTime.now();
    final formattedDate =
        '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';

    final annotations = [
      'Building: $buildingNumber',
      'Room: $roomNumber',
      'Meter: ${meterType.displayName} ${meterType.icon}',
      'Reading: $readingValue ${meterType.unit}',
      'Date: $formattedDate',
    ];

    // Add semi-transparent background for text
    final overlayHeight = annotations.length * 25 + 20;
    img.fillRect(
      annotatedImage,
      x1: 10,
      y1: 10,
      x2: annotatedImage.width - 10,
      y2: 10 + overlayHeight,
      color: img.ColorRgba8(0, 0, 0, AppConstants.annotationBackgroundOpacity),
    );

    // Add text annotations
    for (int i = 0; i < annotations.length; i++) {
      img.drawString(
        annotatedImage,
        annotations[i],
        font: img.arial14,
        x: 20,
        y: 20 + (i * 25),
        color: img.ColorRgba8(255, 255, 255, 255),
      );
    }

    return annotatedImage;
  }

  // Copy image to app directory without annotation
  Future<String> copyImageToAppDirectory(
    File originalImage,
    String buildingNumber,
    String roomNumber,
    MeterType meterType,
  ) async {
    try {
      final imagesDir = await _imagesDirectory;
      final fileName = _generateImageFileName(
        buildingNumber,
        roomNumber,
        meterType,
      );
      final targetPath = path.join(imagesDir.path, fileName);

      await originalImage.copy(targetPath);
      return targetPath;
    } catch (e) {
      throw Exception('Failed to copy image: $e');
    }
  }

  // Get image file from path
  File? getImageFile(String imagePath) {
    if (imagePath.isEmpty) return null;

    final file = File(imagePath);
    return file.existsSync() ? file : null;
  }

  // Delete image file
  Future<bool> deleteImage(String imagePath) async {
    try {
      if (imagePath.isEmpty) return false;

      final file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Get all image files in the images directory
  Future<List<File>> getAllImageFiles() async {
    try {
      final imagesDir = await _imagesDirectory;
      final files = await imagesDir.list().toList();

      return files
          .whereType<File>()
          .where(
            (file) =>
                file.path.toLowerCase().endsWith('.jpg') ||
                file.path.toLowerCase().endsWith('.jpeg') ||
                file.path.toLowerCase().endsWith('.png'),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Clean up orphaned images (images not referenced in database)
  Future<int> cleanupOrphanedImages(List<MeterReading> allReadings) async {
    try {
      final allImageFiles = await getAllImageFiles();
      final referencedPaths = allReadings.map((r) => r.imagePath).toSet();

      int deletedCount = 0;
      for (final file in allImageFiles) {
        if (!referencedPaths.contains(file.path)) {
          try {
            await file.delete();
            deletedCount++;
          } catch (e) {
            // Ignore individual file deletion errors
          }
        }
      }

      return deletedCount;
    } catch (e) {
      return 0;
    }
  }

  // Get image size information
  Future<Size?> getImageSize(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) return null;

      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image != null) {
        return Size(image.width.toDouble(), image.height.toDouble());
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Create thumbnail for image
  Future<String?> createThumbnail(
    String originalImagePath, {
    int maxWidth = 200,
  }) async {
    try {
      final file = File(originalImagePath);
      if (!await file.exists()) return null;

      final bytes = await file.readAsBytes();
      final originalImage = img.decodeImage(bytes);

      if (originalImage == null) return null;

      // Calculate thumbnail size maintaining aspect ratio
      final aspectRatio = originalImage.height / originalImage.width;
      final thumbnailHeight = (maxWidth * aspectRatio).round();

      // Resize image
      final thumbnail = img.copyResize(
        originalImage,
        width: maxWidth,
        height: thumbnailHeight,
      );

      // Save thumbnail
      final imagesDir = await _imagesDirectory;
      final originalFileName = path.basenameWithoutExtension(originalImagePath);
      final thumbnailFileName = '${originalFileName}_thumb.jpg';
      final thumbnailPath = path.join(imagesDir.path, thumbnailFileName);

      final thumbnailBytes = img.encodeJpg(thumbnail, quality: 70);
      await File(thumbnailPath).writeAsBytes(thumbnailBytes);

      return thumbnailPath;
    } catch (e) {
      return null;
    }
  }
}

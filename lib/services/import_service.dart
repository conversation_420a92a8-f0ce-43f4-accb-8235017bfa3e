import 'dart:convert';
import 'dart:io';
import 'package:archive/archive_io.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/meter_reading.dart';
import '../services/database_service.dart';
import '../services/image_service.dart';
import '../utils/constants.dart';

class ImportResult {
  final int totalReadings;
  final int importedReadings;
  final int skippedReadings;
  final int duplicateReadings;
  final List<String> errors;

  ImportResult({
    required this.totalReadings,
    required this.importedReadings,
    required this.skippedReadings,
    required this.duplicateReadings,
    required this.errors,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get isSuccessful => importedReadings > 0 && !hasErrors;
}

class ImportService {
  static final ImportService _instance = ImportService._internal();
  factory ImportService() => _instance;
  ImportService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final ImageService _imageService = ImageService();

  // Get the directory for storing imports
  Future<Directory> get _importsDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final importsDir = Directory(path.join(appDir.path, AppConstants.importsFolder));
    
    if (!await importsDir.exists()) {
      await importsDir.create(recursive: true);
    }
    
    return importsDir;
  }

  // Import readings from ZIP file
  Future<ImportResult> importFromZipFile(String zipFilePath, {bool allowDuplicates = false}) async {
    final errors = <String>[];
    int totalReadings = 0;
    int importedReadings = 0;
    int skippedReadings = 0;
    int duplicateReadings = 0;

    try {
      // Validate ZIP file
      final zipFile = File(zipFilePath);
      if (!await zipFile.exists()) {
        errors.add('ZIP file does not exist');
        return ImportResult(
          totalReadings: 0,
          importedReadings: 0,
          skippedReadings: 0,
          duplicateReadings: 0,
          errors: errors,
        );
      }

      // Extract ZIP file
      final zipBytes = await zipFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(zipBytes);

      // Find and validate data file
      final dataFile = archive.findFile(AppConstants.dataFileName);
      if (dataFile == null) {
        errors.add('Data file not found in ZIP archive');
        return ImportResult(
          totalReadings: 0,
          importedReadings: 0,
          skippedReadings: 0,
          duplicateReadings: 0,
          errors: errors,
        );
      }

      // Parse readings data
      final dataContent = utf8.decode(dataFile.content as List<int>);
      final Map<String, dynamic> data = jsonDecode(dataContent);
      
      if (!data.containsKey('readings')) {
        errors.add('Invalid data format: readings not found');
        return ImportResult(
          totalReadings: 0,
          importedReadings: 0,
          skippedReadings: 0,
          duplicateReadings: 0,
          errors: errors,
        );
      }

      final readingsData = data['readings'] as List<dynamic>;
      totalReadings = readingsData.length;

      // Get existing readings for duplicate detection
      final existingReadings = await _databaseService.getAllReadings();
      final existingKeys = existingReadings.map((r) => r.uniqueKey).toSet();

      // Create temporary directory for extracted images
      final tempDir = await _createTempDirectory();

      try {
        // Extract images from archive
        final imageMap = await _extractImages(archive, tempDir);

        // Process each reading
        for (final readingData in readingsData) {
          try {
            final reading = MeterReading.fromJson(readingData as Map<String, dynamic>);
            
            // Check for duplicates
            if (existingKeys.contains(reading.uniqueKey)) {
              if (!allowDuplicates) {
                duplicateReadings++;
                continue;
              }
            }

            // Handle image import
            String? newImagePath;
            if (reading.imagePath.isNotEmpty) {
              final originalImageName = path.basename(reading.imagePath);
              final tempImagePath = imageMap[originalImageName];
              
              if (tempImagePath != null && await File(tempImagePath).exists()) {
                // Copy image to app directory
                newImagePath = await _imageService.copyImageToAppDirectory(
                  File(tempImagePath),
                  reading.buildingNumber,
                  reading.roomNumber,
                  reading.meterType,
                );
              } else {
                errors.add('Image not found for reading: ${reading.uniqueKey}');
                skippedReadings++;
                continue;
              }
            }

            // Create new reading with updated image path
            final newReading = reading.copyWith(
              id: null, // Let database assign new ID
              imagePath: newImagePath ?? '',
            );

            // Insert into database
            await _databaseService.insertReading(newReading);
            importedReadings++;

          } catch (e) {
            errors.add('Failed to import reading: $e');
            skippedReadings++;
          }
        }

      } finally {
        // Clean up temporary directory
        await _cleanupTempDirectory(tempDir);
      }

    } catch (e) {
      errors.add('Import failed: $e');
    }

    return ImportResult(
      totalReadings: totalReadings,
      importedReadings: importedReadings,
      skippedReadings: skippedReadings,
      duplicateReadings: duplicateReadings,
      errors: errors,
    );
  }

  // Extract images from archive to temporary directory
  Future<Map<String, String>> _extractImages(Archive archive, Directory tempDir) async {
    final imageMap = <String, String>{};

    for (final file in archive) {
      if (file.isFile && file.name.startsWith('images/')) {
        final fileName = path.basename(file.name);
        final tempFilePath = path.join(tempDir.path, fileName);
        
        try {
          final tempFile = File(tempFilePath);
          await tempFile.writeAsBytes(file.content as List<int>);
          imageMap[fileName] = tempFilePath;
        } catch (e) {
          print('Failed to extract image ${file.name}: $e');
        }
      }
    }

    return imageMap;
  }

  // Create temporary directory for import
  Future<Directory> _createTempDirectory() async {
    final importsDir = await _importsDirectory;
    final tempDirName = 'temp_import_${DateTime.now().millisecondsSinceEpoch}';
    final tempDir = Directory(path.join(importsDir.path, tempDirName));
    
    await tempDir.create(recursive: true);
    return tempDir;
  }

  // Clean up temporary directory
  Future<void> _cleanupTempDirectory(Directory tempDir) async {
    try {
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    } catch (e) {
      print('Failed to cleanup temp directory: $e');
    }
  }

  // Validate ZIP file structure
  Future<Map<String, dynamic>> validateZipFile(String zipFilePath) async {
    final result = <String, dynamic>{
      'isValid': false,
      'hasDataFile': false,
      'hasImages': false,
      'readingsCount': 0,
      'imagesCount': 0,
      'errors': <String>[],
    };

    try {
      final zipFile = File(zipFilePath);
      if (!await zipFile.exists()) {
        result['errors'].add('ZIP file does not exist');
        return result;
      }

      final zipBytes = await zipFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(zipBytes);

      // Check for data file
      final dataFile = archive.findFile(AppConstants.dataFileName);
      if (dataFile != null) {
        result['hasDataFile'] = true;
        
        try {
          final dataContent = utf8.decode(dataFile.content as List<int>);
          final data = jsonDecode(dataContent);
          
          if (data.containsKey('readings')) {
            result['readingsCount'] = (data['readings'] as List).length;
          }
        } catch (e) {
          result['errors'].add('Invalid data file format');
        }
      } else {
        result['errors'].add('Data file not found');
      }

      // Check for images
      final imageFiles = archive.where((file) => 
          file.isFile && file.name.startsWith('images/')).toList();
      
      if (imageFiles.isNotEmpty) {
        result['hasImages'] = true;
        result['imagesCount'] = imageFiles.length;
      }

      result['isValid'] = result['hasDataFile'] && result['errors'].isEmpty;

    } catch (e) {
      result['errors'].add('Failed to validate ZIP file: $e');
    }

    return result;
  }

  // Get import file info
  Future<Map<String, dynamic>?> getImportFileInfo(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return null;

      final stat = await file.stat();
      final fileName = path.basename(filePath);
      final validation = await validateZipFile(filePath);
      
      return {
        'fileName': fileName,
        'filePath': filePath,
        'fileSize': stat.size,
        'createdDate': stat.modified,
        'formattedSize': _formatFileSize(stat.size),
        'validation': validation,
      };
    } catch (e) {
      return null;
    }
  }

  // Format file size for display
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // Copy file to imports directory
  Future<String> copyFileToImportsDirectory(String sourceFilePath) async {
    try {
      final sourceFile = File(sourceFilePath);
      if (!await sourceFile.exists()) {
        throw Exception('Source file does not exist');
      }

      final importsDir = await _importsDirectory;
      final fileName = path.basename(sourceFilePath);
      final targetPath = path.join(importsDir.path, fileName);

      await sourceFile.copy(targetPath);
      return targetPath;
    } catch (e) {
      throw Exception('Failed to copy file to imports directory: $e');
    }
  }
}

import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/meter_reading.dart';
import '../models/building_state.dart';
import '../utils/constants.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    
    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create readings table
    await db.execute('''
      CREATE TABLE ${AppConstants.readingsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        building_number TEXT NOT NULL,
        room_number TEXT NOT NULL,
        meter_type TEXT NOT NULL,
        reading_value REAL NOT NULL,
        image_path TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        notes TEXT
      )
    ''');

    // Create buildings state table
    await db.execute('''
      CREATE TABLE ${AppConstants.buildingsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        selected_building_number TEXT,
        current_room_number TEXT,
        last_access_time INTEGER
      )
    ''');

    // Create indexes for better performance
    await db.execute('''
      CREATE INDEX idx_readings_building_room 
      ON ${AppConstants.readingsTable} (building_number, room_number)
    ''');

    await db.execute('''
      CREATE INDEX idx_readings_timestamp 
      ON ${AppConstants.readingsTable} (timestamp)
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Example: Add new column in version 2
      // await db.execute('ALTER TABLE ${AppConstants.readingsTable} ADD COLUMN new_column TEXT');
    }
  }

  // CRUD operations for MeterReading
  Future<int> insertReading(MeterReading reading) async {
    final db = await database;
    return await db.insert(
      AppConstants.readingsTable,
      reading.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<MeterReading>> getAllReadings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.readingsTable,
      orderBy: 'timestamp DESC',
    );

    return List.generate(maps.length, (i) {
      return MeterReading.fromMap(maps[i]);
    });
  }

  Future<List<MeterReading>> getReadingsByBuilding(String buildingNumber) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.readingsTable,
      where: 'building_number = ?',
      whereArgs: [buildingNumber],
      orderBy: 'timestamp DESC',
    );

    return List.generate(maps.length, (i) {
      return MeterReading.fromMap(maps[i]);
    });
  }

  Future<List<MeterReading>> getReadingsByRoom(String buildingNumber, String roomNumber) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.readingsTable,
      where: 'building_number = ? AND room_number = ?',
      whereArgs: [buildingNumber, roomNumber],
      orderBy: 'timestamp DESC',
    );

    return List.generate(maps.length, (i) {
      return MeterReading.fromMap(maps[i]);
    });
  }

  Future<List<MeterReading>> getReadingsByDateRange(DateTime startDate, DateTime endDate) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.readingsTable,
      where: 'timestamp >= ? AND timestamp <= ?',
      whereArgs: [startDate.millisecondsSinceEpoch, endDate.millisecondsSinceEpoch],
      orderBy: 'timestamp DESC',
    );

    return List.generate(maps.length, (i) {
      return MeterReading.fromMap(maps[i]);
    });
  }

  Future<MeterReading?> getReadingById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.readingsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return MeterReading.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateReading(MeterReading reading) async {
    final db = await database;
    return await db.update(
      AppConstants.readingsTable,
      reading.toMap(),
      where: 'id = ?',
      whereArgs: [reading.id],
    );
  }

  Future<int> deleteReading(int id) async {
    final db = await database;
    return await db.delete(
      AppConstants.readingsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Building state operations
  Future<void> saveBuildingState(BuildingState state) async {
    final db = await database;
    
    // Clear existing state and insert new one
    await db.delete(AppConstants.buildingsTable);
    await db.insert(
      AppConstants.buildingsTable,
      state.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<BuildingState?> getBuildingState() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.buildingsTable,
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return BuildingState.fromMap(maps.first);
    }
    return null;
  }

  Future<void> clearBuildingState() async {
    final db = await database;
    await db.delete(AppConstants.buildingsTable);
  }

  // Utility methods
  Future<int> getReadingsCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) FROM ${AppConstants.readingsTable}');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  Future<List<String>> getDistinctBuildings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT DISTINCT building_number FROM ${AppConstants.readingsTable} ORDER BY building_number',
    );

    return maps.map((map) => map['building_number'] as String).toList();
  }

  Future<List<String>> getDistinctRooms(String buildingNumber) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT DISTINCT room_number FROM ${AppConstants.readingsTable} WHERE building_number = ? ORDER BY room_number',
      [buildingNumber],
    );

    return maps.map((map) => map['room_number'] as String).toList();
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}

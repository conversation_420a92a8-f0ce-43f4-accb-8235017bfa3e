import 'dart:io';
import 'package:share_plus/share_plus.dart';
import 'package:path/path.dart' as path;

class ShareService {
  /// Share an export file with other apps
  Future<ShareResult> shareExportFile(
    String filePath, {
    String? subject,
  }) async {
    try {
      final file = File(filePath);

      if (!file.existsSync()) {
        throw Exception('File does not exist: $filePath');
      }

      final fileName = path.basename(filePath);
      final fileSize = await file.length();
      final formattedSize = _formatFileSize(fileSize);

      // Create XFile for sharing
      final xFile = XFile(
        filePath,
        name: fileName,
        mimeType: 'application/zip',
      );

      // Generate share text
      final shareText = _generateShareText(fileName, formattedSize);

      // Share the file
      final result = await Share.shareXFiles(
        [xFile],
        text: shareText,
        subject: subject ?? 'Meter Reading Export - $fileName',
      );

      return result;
    } catch (e) {
      throw Exception('Failed to share file: $e');
    }
  }

  /// Share multiple export files
  Future<ShareResult> shareMultipleExportFiles(
    List<String> filePaths, {
    String? subject,
  }) async {
    try {
      final xFiles = <XFile>[];
      int totalSize = 0;

      for (final filePath in filePaths) {
        final file = File(filePath);

        if (!file.existsSync()) {
          throw Exception('File does not exist: $filePath');
        }

        final fileName = path.basename(filePath);
        final fileSize = await file.length();
        totalSize += fileSize;

        xFiles.add(
          XFile(filePath, name: fileName, mimeType: 'application/zip'),
        );
      }

      final formattedTotalSize = _formatFileSize(totalSize);
      final shareText = _generateMultipleShareText(
        xFiles.length,
        formattedTotalSize,
      );

      // Share the files
      final result = await Share.shareXFiles(
        xFiles,
        text: shareText,
        subject: subject ?? 'Meter Reading Exports (${xFiles.length} files)',
      );

      return result;
    } catch (e) {
      throw Exception('Failed to share files: $e');
    }
  }

  /// Share export file information as text
  Future<void> shareExportInfo(Map<String, dynamic> exportInfo) async {
    try {
      final shareText = _generateExportInfoText(exportInfo);

      await Share.share(shareText, subject: 'Meter Reading Export Information');
    } catch (e) {
      throw Exception('Failed to share export info: $e');
    }
  }

  /// Share reading data as CSV text
  Future<void> shareReadingsAsText(
    List<Map<String, dynamic>> readings, {
    String? title,
  }) async {
    try {
      final csvText = _generateCSVText(readings);

      await Share.share(csvText, subject: title ?? 'Meter Readings Data');
    } catch (e) {
      throw Exception('Failed to share readings as text: $e');
    }
  }

  /// Check if sharing is available on the platform
  Future<bool> canShare() async {
    try {
      // Share Plus automatically handles platform availability
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get available share targets (if supported by platform)
  Future<List<String>> getAvailableShareTargets() async {
    try {
      // This is a placeholder - Share Plus doesn't provide this info directly
      // But we can return common share targets based on platform
      if (Platform.isAndroid) {
        return [
          'Email',
          'Messages',
          'WhatsApp',
          'Telegram',
          'Google Drive',
          'Dropbox',
          'Bluetooth',
          'Nearby Share',
        ];
      } else if (Platform.isIOS) {
        return [
          'Mail',
          'Messages',
          'WhatsApp',
          'Telegram',
          'iCloud Drive',
          'Dropbox',
          'AirDrop',
        ];
      } else {
        return ['Email', 'File Manager'];
      }
    } catch (e) {
      return [];
    }
  }

  // Private helper methods

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _generateShareText(String fileName, String fileSize) {
    return '''
📊 Meter Reading Export

📁 File: $fileName
📏 Size: $fileSize
📅 Shared: ${DateTime.now().toString().split('.')[0]}

This file contains meter reading data exported from the Meter Reading App.
Import this file using the app's import function to restore the data.

🔧 Compatible with: Meter Reading App v1.0+
''';
  }

  String _generateMultipleShareText(int fileCount, String totalSize) {
    return '''
📊 Meter Reading Exports

📁 Files: $fileCount export files
📏 Total Size: $totalSize
📅 Shared: ${DateTime.now().toString().split('.')[0]}

These files contain meter reading data exported from the Meter Reading App.
Import these files using the app's import function to restore the data.

🔧 Compatible with: Meter Reading App v1.0+
''';
  }

  String _generateExportInfoText(Map<String, dynamic> info) {
    final buffer = StringBuffer();
    buffer.writeln('📊 Meter Reading Export Information');
    buffer.writeln('');
    buffer.writeln('📁 File: ${info['fileName'] ?? 'Unknown'}');
    buffer.writeln('📏 Size: ${info['formattedSize'] ?? 'Unknown'}');
    buffer.writeln('📅 Created: ${info['createdDate'] ?? 'Unknown'}');
    buffer.writeln('');

    if (info.containsKey('readingsCount')) {
      buffer.writeln('📊 Data Summary:');
      buffer.writeln('• Readings: ${info['readingsCount']}');
      if (info.containsKey('buildingsCount')) {
        buffer.writeln('• Buildings: ${info['buildingsCount']}');
      }
      if (info.containsKey('roomsCount')) {
        buffer.writeln('• Rooms: ${info['roomsCount']}');
      }
      if (info.containsKey('imagesCount')) {
        buffer.writeln('• Images: ${info['imagesCount']}');
      }
    }

    buffer.writeln('');
    buffer.writeln('🔧 Compatible with: Meter Reading App v1.0+');

    return buffer.toString();
  }

  String _generateCSVText(List<Map<String, dynamic>> readings) {
    if (readings.isEmpty) {
      return 'No readings data available';
    }

    final buffer = StringBuffer();

    // CSV Header
    buffer.writeln(
      'Building,Room,Meter Type,Reading Value,Unit,Timestamp,Notes',
    );

    // CSV Data
    for (final reading in readings) {
      final building = reading['buildingNumber'] ?? '';
      final room = reading['roomNumber'] ?? '';
      final meterType = reading['meterType'] ?? '';
      final value = reading['readingValue'] ?? '';
      final unit = reading['unit'] ?? '';
      final timestamp = reading['timestamp'] ?? '';
      final notes = (reading['notes'] ?? '').replaceAll(
        ',',
        ';',
      ); // Escape commas

      buffer.writeln(
        '$building,$room,$meterType,$value,$unit,$timestamp,"$notes"',
      );
    }

    return buffer.toString();
  }
}

// Using ShareResult from share_plus package directly

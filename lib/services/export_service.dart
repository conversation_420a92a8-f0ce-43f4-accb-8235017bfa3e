import 'dart:convert';
import 'dart:io';
import 'package:archive/archive_io.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/meter_reading.dart';
import '../services/database_service.dart';
import '../services/image_service.dart';
import '../utils/constants.dart';

class ExportService {
  static final ExportService _instance = ExportService._internal();
  factory ExportService() => _instance;
  ExportService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final ImageService _imageService = ImageService();

  // Get the directory for storing exports
  Future<Directory> get _exportsDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final exportsDir = Directory(path.join(appDir.path, AppConstants.exportsFolder));
    
    if (!await exportsDir.exists()) {
      await exportsDir.create(recursive: true);
    }
    
    return exportsDir;
  }

  // Export all readings with images as ZIP file
  Future<String> exportAllReadings() async {
    final readings = await _databaseService.getAllReadings();
    return await _exportReadings(readings, 'all_readings');
  }

  // Export readings by date range
  Future<String> exportReadingsByDateRange(DateTime startDate, DateTime endDate) async {
    final readings = await _databaseService.getReadingsByDateRange(startDate, endDate);
    final dateRangeStr = '${_formatDateForFilename(startDate)}_to_${_formatDateForFilename(endDate)}';
    return await _exportReadings(readings, 'readings_$dateRangeStr');
  }

  // Export readings by building
  Future<String> exportReadingsByBuilding(String buildingNumber) async {
    final readings = await _databaseService.getReadingsByBuilding(buildingNumber);
    return await _exportReadings(readings, 'building_$buildingNumber');
  }

  // Main export function
  Future<String> _exportReadings(List<MeterReading> readings, String filenameSuffix) async {
    try {
      final exportsDir = await _exportsDirectory;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final zipFileName = '${AppConstants.exportFilePrefix}${filenameSuffix}_$timestamp${AppConstants.exportFileExtension}';
      final zipFilePath = path.join(exportsDir.path, zipFileName);

      // Create archive
      final archive = Archive();

      // Add readings data as JSON
      final readingsJson = _createReadingsJson(readings);
      final dataFile = ArchiveFile(
        AppConstants.dataFileName,
        readingsJson.length,
        utf8.encode(readingsJson),
      );
      archive.addFile(dataFile);

      // Add metadata
      final metadata = _createMetadata(readings);
      final metadataFile = ArchiveFile(
        'metadata.json',
        metadata.length,
        utf8.encode(metadata),
      );
      archive.addFile(metadataFile);

      // Add images
      await _addImagesToArchive(archive, readings);

      // Create ZIP file
      final zipEncoder = ZipEncoder();
      final zipBytes = zipEncoder.encode(archive);
      
      if (zipBytes != null) {
        await File(zipFilePath).writeAsBytes(zipBytes);
        return zipFilePath;
      } else {
        throw Exception('Failed to create ZIP file');
      }
    } catch (e) {
      throw Exception('Export failed: $e');
    }
  }

  // Create JSON string from readings
  String _createReadingsJson(List<MeterReading> readings) {
    final readingsData = {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'totalReadings': readings.length,
      'readings': readings.map((reading) => reading.toJson()).toList(),
    };

    return const JsonEncoder.withIndent('  ').convert(readingsData);
  }

  // Create metadata JSON
  String _createMetadata(List<MeterReading> readings) {
    final buildings = <String>{};
    final rooms = <String>{};
    final meterTypes = <String>{};
    DateTime? earliestReading;
    DateTime? latestReading;

    for (final reading in readings) {
      buildings.add(reading.buildingNumber);
      rooms.add('${reading.buildingNumber}-${reading.roomNumber}');
      meterTypes.add(reading.meterType.displayName);

      if (earliestReading == null || reading.timestamp.isBefore(earliestReading)) {
        earliestReading = reading.timestamp;
      }
      if (latestReading == null || reading.timestamp.isAfter(latestReading)) {
        latestReading = reading.timestamp;
      }
    }

    final metadata = {
      'exportInfo': {
        'version': '1.0',
        'exportDate': DateTime.now().toIso8601String(),
        'totalReadings': readings.length,
        'totalImages': readings.length,
      },
      'dataRange': {
        'earliestReading': earliestReading?.toIso8601String(),
        'latestReading': latestReading?.toIso8601String(),
      },
      'summary': {
        'totalBuildings': buildings.length,
        'totalRooms': rooms.length,
        'meterTypes': meterTypes.toList(),
        'buildings': buildings.toList()..sort(),
      },
    };

    return const JsonEncoder.withIndent('  ').convert(metadata);
  }

  // Add images to archive
  Future<void> _addImagesToArchive(Archive archive, List<MeterReading> readings) async {
    for (final reading in readings) {
      final imageFile = _imageService.getImageFile(reading.imagePath);
      if (imageFile != null && await imageFile.exists()) {
        try {
          final imageBytes = await imageFile.readAsBytes();
          final imageFileName = path.basename(reading.imagePath);
          
          final archiveFile = ArchiveFile(
            'images/$imageFileName',
            imageBytes.length,
            imageBytes,
          );
          archive.addFile(archiveFile);
        } catch (e) {
          // Log error but continue with other images
          print('Failed to add image ${reading.imagePath} to archive: $e');
        }
      }
    }
  }

  // Format date for filename
  String _formatDateForFilename(DateTime date) {
    return '${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}';
  }

  // Get list of available export files
  Future<List<File>> getExportFiles() async {
    try {
      final exportsDir = await _exportsDirectory;
      final files = await exportsDir.list().toList();
      
      return files
          .whereType<File>()
          .where((file) => file.path.endsWith(AppConstants.exportFileExtension))
          .toList()
        ..sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));
    } catch (e) {
      return [];
    }
  }

  // Delete export file
  Future<bool> deleteExportFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Get export file info
  Future<Map<String, dynamic>?> getExportFileInfo(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return null;

      final stat = await file.stat();
      final fileName = path.basename(filePath);
      
      return {
        'fileName': fileName,
        'filePath': filePath,
        'fileSize': stat.size,
        'createdDate': stat.modified,
        'formattedSize': _formatFileSize(stat.size),
      };
    } catch (e) {
      return null;
    }
  }

  // Format file size for display
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // Clean up old export files (keep only last N files)
  Future<int> cleanupOldExports({int keepCount = 10}) async {
    try {
      final exportFiles = await getExportFiles();
      if (exportFiles.length <= keepCount) return 0;

      final filesToDelete = exportFiles.skip(keepCount).toList();
      int deletedCount = 0;

      for (final file in filesToDelete) {
        try {
          await file.delete();
          deletedCount++;
        } catch (e) {
          // Ignore individual file deletion errors
        }
      }

      return deletedCount;
    } catch (e) {
      return 0;
    }
  }
}

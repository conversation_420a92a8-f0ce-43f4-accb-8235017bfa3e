enum MeterType {
  main,
  generator,
  utility,
  water;

  String get displayName {
    switch (this) {
      case MeterType.main:
        return 'Main';
      case MeterType.generator:
        return 'Generator';
      case MeterType.utility:
        return 'Utility';
      case MeterType.water:
        return 'Water';
    }
  }

  String get unit {
    switch (this) {
      case MeterType.main:
      case MeterType.generator:
      case MeterType.utility:
        return 'kWh';
      case MeterType.water:
        return 'm³';
    }
  }

  String get icon {
    switch (this) {
      case MeterType.main:
        return '⚡';
      case MeterType.generator:
        return '🔌';
      case MeterType.utility:
        return '💡';
      case MeterType.water:
        return '💧';
    }
  }

  static MeterType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'main':
        return MeterType.main;
      case 'generator':
        return MeterType.generator;
      case 'utility':
        return MeterType.utility;
      case 'water':
        return MeterType.water;
      default:
        return MeterType.main;
    }
  }
}

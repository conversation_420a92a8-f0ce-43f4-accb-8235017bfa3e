class BuildingState {
  final String? selectedBuildingNumber;
  final String? currentRoomNumber;
  final DateTime? lastAccessTime;

  BuildingState({
    this.selectedBuildingNumber,
    this.currentRoomNumber,
    this.lastAccessTime,
  });

  // Convert to Map for storage
  Map<String, dynamic> toMap() {
    return {
      'selected_building_number': selectedBuildingNumber,
      'current_room_number': currentRoomNumber,
      'last_access_time': lastAccessTime?.millisecondsSinceEpoch,
    };
  }

  // Create from Map
  factory BuildingState.fromMap(Map<String, dynamic> map) {
    return BuildingState(
      selectedBuildingNumber: map['selected_building_number'],
      currentRoomNumber: map['current_room_number'],
      lastAccessTime: map['last_access_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_access_time'])
          : null,
    );
  }

  // Create a copy with updated values
  BuildingState copyWith({
    String? selectedBuildingNumber,
    String? currentRoomNumber,
    DateTime? lastAccessTime,
  }) {
    return BuildingState(
      selectedBuildingNumber: selectedBuildingNumber ?? this.selectedBuildingNumber,
      currentRoomNumber: currentRoomNumber ?? this.currentRoomNumber,
      lastAccessTime: lastAccessTime ?? this.lastAccessTime,
    );
  }

  // Clear all state
  BuildingState clear() {
    return BuildingState();
  }

  @override
  String toString() {
    return 'BuildingState{building: $selectedBuildingNumber, room: $currentRoomNumber, lastAccess: $lastAccessTime}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BuildingState &&
        other.selectedBuildingNumber == selectedBuildingNumber &&
        other.currentRoomNumber == currentRoomNumber &&
        other.lastAccessTime == lastAccessTime;
  }

  @override
  int get hashCode {
    return selectedBuildingNumber.hashCode ^
        currentRoomNumber.hashCode ^
        lastAccessTime.hashCode;
  }
}

import 'meter_type.dart';

class MeterReading {
  final int? id;
  final String buildingNumber;
  final String roomNumber;
  final MeterType meterType;
  final double readingValue;
  final String imagePath;
  final DateTime timestamp;
  final String? notes;

  MeterReading({
    this.id,
    required this.buildingNumber,
    required this.roomNumber,
    required this.meterType,
    required this.readingValue,
    required this.imagePath,
    required this.timestamp,
    this.notes,
  });

  // Convert to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'building_number': buildingNumber,
      'room_number': roomNumber,
      'meter_type': meterType.displayName,
      'reading_value': readingValue,
      'image_path': imagePath,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'notes': notes,
    };
  }

  // Create from Map (database retrieval)
  factory MeterReading.fromMap(Map<String, dynamic> map) {
    return MeterReading(
      id: map['id']?.toInt(),
      buildingNumber: map['building_number'] ?? '',
      roomNumber: map['room_number'] ?? '',
      meterType: MeterType.fromString(map['meter_type'] ?? 'main'),
      readingValue: map['reading_value']?.toDouble() ?? 0.0,
      imagePath: map['image_path'] ?? '',
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] ?? 0),
      notes: map['notes'],
    );
  }

  // Convert to JSON for export
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'buildingNumber': buildingNumber,
      'roomNumber': roomNumber,
      'meterType': meterType.displayName,
      'readingValue': readingValue,
      'imagePath': imagePath,
      'timestamp': timestamp.toIso8601String(),
      'notes': notes,
    };
  }

  // Create from JSON (import)
  factory MeterReading.fromJson(Map<String, dynamic> json) {
    return MeterReading(
      id: json['id']?.toInt(),
      buildingNumber: json['buildingNumber'] ?? '',
      roomNumber: json['roomNumber'] ?? '',
      meterType: MeterType.fromString(json['meterType'] ?? 'main'),
      readingValue: json['readingValue']?.toDouble() ?? 0.0,
      imagePath: json['imagePath'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      notes: json['notes'],
    );
  }

  // Create a copy with updated values
  MeterReading copyWith({
    int? id,
    String? buildingNumber,
    String? roomNumber,
    MeterType? meterType,
    double? readingValue,
    String? imagePath,
    DateTime? timestamp,
    String? notes,
  }) {
    return MeterReading(
      id: id ?? this.id,
      buildingNumber: buildingNumber ?? this.buildingNumber,
      roomNumber: roomNumber ?? this.roomNumber,
      meterType: meterType ?? this.meterType,
      readingValue: readingValue ?? this.readingValue,
      imagePath: imagePath ?? this.imagePath,
      timestamp: timestamp ?? this.timestamp,
      notes: notes ?? this.notes,
    );
  }

  // Generate unique identifier for duplicate detection
  String get uniqueKey => '${buildingNumber}_${roomNumber}_${meterType.displayName}';

  @override
  String toString() {
    return 'MeterReading{id: $id, building: $buildingNumber, room: $roomNumber, type: ${meterType.displayName}, value: $readingValue, timestamp: $timestamp}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MeterReading &&
        other.buildingNumber == buildingNumber &&
        other.roomNumber == roomNumber &&
        other.meterType == meterType &&
        other.readingValue == readingValue &&
        other.imagePath == imagePath &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return buildingNumber.hashCode ^
        roomNumber.hashCode ^
        meterType.hashCode ^
        readingValue.hashCode ^
        imagePath.hashCode ^
        timestamp.hashCode;
  }
}

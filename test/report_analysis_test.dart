import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:meter_pj/services/report_service.dart';
import 'package:meter_pj/models/meter_reading.dart';
import 'package:meter_pj/models/meter_type.dart';
import 'package:meter_pj/screens/report_analysis_screen.dart';

void main() {
  group('Report Analysis Tests', () {
    late ReportService reportService;
    late List<MeterReading> testReadings;

    setUp(() {
      reportService = ReportService();

      // Create test data
      testReadings = [
        MeterReading(
          buildingNumber: '123',
          roomNumber: '101',
          meterType: MeterType.main,
          readingValue: 1000.0,
          imagePath: '/test/image1.jpg',
          timestamp: DateTime(2024, 1, 15, 10, 30),
          notes: 'January reading',
        ),
        MeterReading(
          buildingNumber: '123',
          roomNumber: '102',
          meterType: MeterType.water,
          readingValue: 500.0,
          imagePath: '/test/image2.jpg',
          timestamp: DateTime(2024, 2, 15, 10, 30),
          notes: 'February reading',
        ),
        MeterReading(
          buildingNumber: '456',
          roomNumber: '201',
          meterType: MeterType.generator,
          readingValue: 750.0,
          imagePath: '/test/image3.jpg',
          timestamp: DateTime(2024, 3, 15, 10, 30),
          notes: 'March reading',
        ),
        MeterReading(
          buildingNumber: '456',
          roomNumber: '202',
          meterType: MeterType.utility,
          readingValue: 300.0,
          imagePath: '/test/image4.jpg',
          timestamp: DateTime(2024, 4, 15, 10, 30),
          notes: 'April reading',
        ),
        MeterReading(
          buildingNumber: '123',
          roomNumber: '101',
          meterType: MeterType.main,
          readingValue: 1200.0,
          imagePath: '/test/image5.jpg',
          timestamp: DateTime(2024, 6, 15, 10, 30),
          notes: 'June reading',
        ),
      ];
    });

    test('ReportService can be instantiated', () {
      expect(reportService, isNotNull);
    });

    test('Monthly report generation works correctly', () {
      final report = reportService.generateMonthlyReport(testReadings, 2024);

      expect(report, isNotNull);
      expect(report['year'], equals(2024));
      expect(report['totalReadings'], equals(5));
      expect(report['totalBuildings'], equals(2));
      expect(report['totalRooms'], equals(4));

      final monthlyData =
          report['monthlyData'] as Map<int, Map<String, dynamic>>;
      expect(monthlyData, isNotNull);
      expect(monthlyData.length, equals(12));

      // Check January data
      expect(monthlyData[1]!['totalReadings'], equals(1));
      expect((monthlyData[1]!['buildings'] as Set).length, equals(1));

      // Check that empty months have zero readings
      expect(monthlyData[5]!['totalReadings'], equals(0));
    });

    test('Yearly report generation works correctly', () {
      final report = reportService.generateYearlyReport(testReadings);

      expect(report, isNotNull);
      expect(report['totalReadings'], equals(5));

      final availableYears = report['availableYears'] as List<int>;
      expect(availableYears, contains(2024));

      final yearlyData = report['yearlyData'] as Map<int, Map<String, dynamic>>;
      expect(yearlyData[2024], isNotNull);
      expect(yearlyData[2024]!['totalReadings'], equals(5));
    });

    test('Building report generation works correctly', () {
      final report = reportService.generateBuildingReport(testReadings);

      expect(report, isNotNull);
      expect(report['totalBuildings'], equals(2));

      final buildingData =
          report['buildingData'] as Map<String, Map<String, dynamic>>;
      expect(buildingData['123'], isNotNull);
      expect(buildingData['456'], isNotNull);

      // Building 123 should have 3 readings (2 main + 1 water)
      expect(buildingData['123']!['totalReadings'], equals(3));
      expect(buildingData['123']!['rooms'], equals(2));

      // Building 456 should have 2 readings
      expect(buildingData['456']!['totalReadings'], equals(2));
      expect(buildingData['456']!['rooms'], equals(2));
    });

    test('Consumption trends generation works correctly', () {
      final report = reportService.generateConsumptionTrends(testReadings);

      expect(report, isNotNull);
      expect(report['monthlyTotals'], isNotNull);
      expect(report['meterTypeTrends'], isNotNull);
      expect(report['trendDirection'], isNotNull);
      expect(report['seasonalPatterns'], isNotNull);
      expect(report['predictions'], isNotNull);

      final monthlyTotals = report['monthlyTotals'] as Map<DateTime, double>;
      expect(monthlyTotals.isNotEmpty, isTrue);

      final meterTypeTrends =
          report['meterTypeTrends'] as Map<MeterType, List<double>>;
      expect(meterTypeTrends.containsKey(MeterType.main), isTrue);
      expect(meterTypeTrends.containsKey(MeterType.water), isTrue);
    });

    test('Peak month calculation works correctly', () {
      final report = reportService.generateMonthlyReport(testReadings, 2024);
      final peakMonth = report['peakMonth'] as Map<String, dynamic>;

      expect(peakMonth, isNotNull);
      expect(peakMonth['month'], isA<int>());
      expect(peakMonth['meterType'], isA<MeterType>());
      expect(peakMonth['consumption'], isA<double>());
      expect(peakMonth['month'], greaterThan(0));
      expect(peakMonth['month'], lessThanOrEqualTo(12));
    });

    test('Monthly trends calculation works correctly', () {
      final report = reportService.generateMonthlyReport(testReadings, 2024);
      final monthlyTrends =
          report['monthlyTrends'] as Map<String, List<double>>;

      expect(monthlyTrends, isNotNull);
      expect(monthlyTrends.containsKey('Main'), isTrue);
      expect(monthlyTrends.containsKey('Water'), isTrue);
      expect(monthlyTrends.containsKey('Generator'), isTrue);
      expect(monthlyTrends.containsKey('Utility'), isTrue);

      // Each meter type should have 12 months of data
      for (final trend in monthlyTrends.values) {
        expect(trend.length, equals(12));
      }
    });

    test('Building efficiency calculation works correctly', () {
      final report = reportService.generateBuildingReport(testReadings);
      final buildingData =
          report['buildingData'] as Map<String, Map<String, dynamic>>;

      for (final building in buildingData.values) {
        expect(building['efficiency'], isA<double>());
        expect(building['efficiency'], greaterThanOrEqualTo(0));
      }
    });

    test('Seasonal patterns identification works correctly', () {
      final report = reportService.generateConsumptionTrends(testReadings);
      final seasonalPatterns =
          report['seasonalPatterns'] as Map<String, double>;

      expect(seasonalPatterns, isNotNull);
      expect(seasonalPatterns.containsKey('Spring'), isTrue);
      expect(seasonalPatterns.containsKey('Summer'), isTrue);
      expect(seasonalPatterns.containsKey('Autumn'), isTrue);
      expect(seasonalPatterns.containsKey('Winter'), isTrue);

      for (final pattern in seasonalPatterns.values) {
        expect(pattern, greaterThanOrEqualTo(0));
      }
    });

    test('Growth rate calculation works correctly', () {
      // Add readings for multiple years
      final multiYearReadings = [
        ...testReadings,
        MeterReading(
          buildingNumber: '123',
          roomNumber: '101',
          meterType: MeterType.main,
          readingValue: 1500.0,
          imagePath: '/test/image6.jpg',
          timestamp: DateTime(2023, 1, 15, 10, 30),
          notes: '2023 reading',
        ),
      ];

      final report = reportService.generateYearlyReport(multiYearReadings);
      final yearlyData = report['yearlyData'] as Map<int, Map<String, dynamic>>;

      // Check that growth rate is calculated for 2024
      if (yearlyData.containsKey(2024)) {
        expect(yearlyData[2024]!['growthRate'], isA<double>());
      }
    });

    test('Empty readings list handled correctly', () {
      final emptyReport = reportService.generateMonthlyReport([], 2024);

      expect(emptyReport['totalReadings'], equals(0));
      expect(emptyReport['totalBuildings'], equals(0));
      expect(emptyReport['totalRooms'], equals(0));

      final monthlyData =
          emptyReport['monthlyData'] as Map<int, Map<String, dynamic>>;
      for (int month = 1; month <= 12; month++) {
        expect(monthlyData[month]!['totalReadings'], equals(0));
      }
    });

    test('Single reading handled correctly', () {
      final singleReading = [testReadings.first];
      final report = reportService.generateMonthlyReport(singleReading, 2024);

      expect(report['totalReadings'], equals(1));
      expect(report['totalBuildings'], equals(1));
      expect(report['totalRooms'], equals(1));
    });

    test('Predictions generation works correctly', () {
      final report = reportService.generateConsumptionTrends(testReadings);
      final predictions = report['predictions'] as List<double>;

      expect(predictions, isNotNull);
      expect(
        predictions.length,
        lessThanOrEqualTo(3),
      ); // Up to 3 months prediction

      for (final prediction in predictions) {
        expect(prediction, isA<double>());
      }
    });

    test('Trend direction calculation works correctly', () {
      final report = reportService.generateConsumptionTrends(testReadings);
      final trendDirection = report['trendDirection'] as String;

      expect(trendDirection, isNotNull);
      expect([
        'increasing',
        'decreasing',
        'stable',
        'insufficient_data',
      ], contains(trendDirection));
    });

    testWidgets('Report analysis screen displays correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: ReportAnalysisScreen())),
      );
      await tester.pump();

      // Verify the screen loads
      expect(find.text('Report Analysis'), findsOneWidget);
      expect(find.byType(TabBar), findsOneWidget);

      // Verify tabs are present
      expect(find.text('Monthly'), findsOneWidget);
      expect(find.text('Yearly'), findsOneWidget);
      expect(find.text('Trends'), findsOneWidget);
    });

    testWidgets('Report analysis screen has period selector', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: ReportAnalysisScreen())),
      );
      await tester.pump();

      // Verify period selector is present
      expect(find.text('Year'), findsOneWidget);
      expect(find.text('Period'), findsOneWidget);
      expect(find.byType(DropdownButtonFormField<int>), findsOneWidget);
      expect(find.byType(DropdownButtonFormField<String>), findsOneWidget);
    });

    testWidgets('Report analysis screen has action menu', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: ReportAnalysisScreen())),
      );
      await tester.pump();

      // Verify action menu is present
      expect(find.byType(PopupMenuButton<String>), findsOneWidget);
      expect(find.byIcon(Icons.refresh), findsOneWidget);
    });

    test('Report service handles different meter types correctly', () {
      final report = reportService.generateMonthlyReport(testReadings, 2024);
      final monthlyData =
          report['monthlyData'] as Map<int, Map<String, dynamic>>;

      // Check that all meter types are tracked
      for (int month = 1; month <= 12; month++) {
        final totalConsumption =
            monthlyData[month]!['totalConsumption'] as Map<MeterType, double>;
        expect(totalConsumption.containsKey(MeterType.main), isTrue);
        expect(totalConsumption.containsKey(MeterType.water), isTrue);
        expect(totalConsumption.containsKey(MeterType.generator), isTrue);
        expect(totalConsumption.containsKey(MeterType.utility), isTrue);
      }
    });

    test('Report service calculates averages correctly', () {
      final report = reportService.generateMonthlyReport(testReadings, 2024);

      expect(report['averageMonthlyReadings'], isA<double>());
      expect(
        report['averageMonthlyReadings'],
        equals(testReadings.length / 12),
      );
    });

    test('Building report identifies most efficient building', () {
      final report = reportService.generateBuildingReport(testReadings);

      expect(report['mostEfficientBuilding'], isA<String>());
      expect(report['highestConsumptionBuilding'], isA<String>());
      expect(report['averageRoomsPerBuilding'], isA<double>());
    });
  });
}

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:meter_pj/models/meter_type.dart';
import 'package:meter_pj/models/meter_reading.dart';
import 'package:meter_pj/providers/building_provider.dart';

void main() {
  group('Simple Reading Tests', () {
    test('Meter types are properly defined', () {
      // Test that all meter types have required properties
      expect(MeterType.values.length, equals(4));

      for (final meterType in MeterType.values) {
        expect(meterType.displayName.isNotEmpty, isTrue);
        expect(meterType.unit.isNotEmpty, isTrue);
        expect(meterType.icon.isNotEmpty, isTrue);
      }

      // Test specific meter types
      expect(MeterType.main.displayName, equals('Main'));
      expect(MeterType.generator.displayName, equals('Generator'));
      expect(MeterType.utility.displayName, equals('Utility'));
      expect(MeterType.water.displayName, equals('Water'));
    });

    test('Meter reading model creation', () {
      final reading = MeterReading(
        buildingNumber: '123',
        roomNumber: '456',
        meterType: MeterType.main,
        readingValue: 1234.56,
        imagePath: '/path/to/image.jpg',
        timestamp: DateTime.now(),
        notes: 'Test reading',
      );

      expect(reading.buildingNumber, equals('123'));
      expect(reading.roomNumber, equals('456'));
      expect(reading.meterType, equals(MeterType.main));
      expect(reading.readingValue, equals(1234.56));
      expect(reading.imagePath, equals('/path/to/image.jpg'));
      expect(reading.notes, equals('Test reading'));
    });

    test('Building number validation', () {
      final container = ProviderContainer();
      final notifier = container.read(buildingProvider.notifier);

      // Test valid building numbers
      expect(notifier.isValidBuildingNumber('1'), isTrue);
      expect(notifier.isValidBuildingNumber('123'), isTrue);
      expect(notifier.isValidBuildingNumber('9999'), isTrue);

      // Test invalid building numbers
      expect(notifier.isValidBuildingNumber(''), isFalse);
      expect(notifier.isValidBuildingNumber('0'), isFalse);
      expect(notifier.isValidBuildingNumber('10000'), isFalse);
      expect(notifier.isValidBuildingNumber('abc'), isFalse);
      expect(notifier.isValidBuildingNumber('-1'), isFalse);

      container.dispose();
    });

    test('Room number validation', () {
      final container = ProviderContainer();
      final notifier = container.read(buildingProvider.notifier);

      // Test valid room numbers
      expect(notifier.isValidRoomNumber('1'), isTrue);
      expect(notifier.isValidRoomNumber('123'), isTrue);
      expect(notifier.isValidRoomNumber('9999'), isTrue);

      // Test invalid room numbers
      expect(notifier.isValidRoomNumber(''), isFalse);
      expect(notifier.isValidRoomNumber('0'), isFalse);
      expect(notifier.isValidRoomNumber('10000'), isFalse);
      expect(notifier.isValidRoomNumber('abc'), isFalse);
      expect(notifier.isValidRoomNumber('-1'), isFalse);

      container.dispose();
    });

    test('Building state management', () {
      final container = ProviderContainer();
      final notifier = container.read(buildingProvider.notifier);

      // Test initial state
      expect(container.read(buildingProvider).selectedBuildingNumber, isNull);
      expect(container.read(buildingProvider).currentRoomNumber, isNull);
      expect(notifier.hasBuildingSelected, isFalse);
      expect(notifier.hasRoomSelected, isFalse);
      expect(notifier.isStateComplete, isFalse);

      container.dispose();
    });

    test('Meter type properties', () {
      // Test Main meter
      expect(MeterType.main.displayName, equals('Main'));
      expect(MeterType.main.unit, equals('kWh'));
      expect(MeterType.main.icon, equals('⚡'));

      // Test Generator meter
      expect(MeterType.generator.displayName, equals('Generator'));
      expect(MeterType.generator.unit, equals('kWh'));
      expect(MeterType.generator.icon, equals('🔌'));

      // Test Utility meter
      expect(MeterType.utility.displayName, equals('Utility'));
      expect(MeterType.utility.unit, equals('kWh'));
      expect(MeterType.utility.icon, equals('💡'));

      // Test Water meter
      expect(MeterType.water.displayName, equals('Water'));
      expect(MeterType.water.unit, equals('m³'));
      expect(MeterType.water.icon, equals('💧'));
    });

    test('Reading value validation scenarios', () {
      // Test valid reading values
      const validValues = [0.0, 1.0, 123.45, 999999.99];
      for (final value in validValues) {
        expect(
          value >= 0,
          isTrue,
          reason: 'Value $value should be non-negative',
        );
        expect(
          value <= 999999.99,
          isTrue,
          reason: 'Value $value should be within range',
        );
      }

      // Test invalid reading values
      const invalidValues = [-1.0, -0.1, 1000000.0];
      for (final value in invalidValues) {
        if (value < 0) {
          expect(value < 0, isTrue, reason: 'Value $value should be negative');
        } else {
          expect(
            value > 999999.99,
            isTrue,
            reason: 'Value $value should be too large',
          );
        }
      }
    });
  });
}

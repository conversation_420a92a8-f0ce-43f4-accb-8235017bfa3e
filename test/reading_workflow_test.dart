import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:meter_pj/main.dart';
import 'package:meter_pj/models/meter_type.dart';
import 'package:meter_pj/providers/building_provider.dart';
import 'package:meter_pj/providers/reading_provider.dart';

void main() {
  group('Reading Workflow Tests', () {
    testWidgets('Building selection workflow', (WidgetTester tester) async {
      // Build the app
      await tester.pumpWidget(const ProviderScope(child: MeterReadingApp()));
      await tester.pumpAndSettle();

      // Verify home screen is displayed
      expect(find.text('Meter Reading App'), findsOneWidget);
      expect(find.text('No building selected'), findsOneWidget);

      // Tap on building selection
      await tester.tap(find.text('Select Building'));
      await tester.pumpAndSettle();

      // Verify building selection screen
      expect(find.text('Select Building'), findsOneWidget);
      expect(find.text('Building Number'), findsOneWidget);
    });

    testWidgets('Room entry workflow', (WidgetTester tester) async {
      // Create a container with providers
      final container = ProviderContainer();
      
      // Set a building first
      await container.read(buildingProvider.notifier).setSelectedBuilding('123');
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: const MeterReadingApp(),
        ),
      );
      await tester.pumpAndSettle();

      // Navigate to room entry
      await tester.tap(find.text('Enter Room'));
      await tester.pumpAndSettle();

      // Verify room entry screen
      expect(find.text('Enter Room'), findsOneWidget);
      expect(find.text('Building 123'), findsOneWidget);
    });

    testWidgets('Meter type display test', (WidgetTester tester) async {
      // Test that all meter types are properly defined
      expect(MeterType.values.length, equals(4));
      
      for (final meterType in MeterType.values) {
        expect(meterType.displayName.isNotEmpty, isTrue);
        expect(meterType.unit.isNotEmpty, isTrue);
        expect(meterType.icon.isNotEmpty, isTrue);
      }
    });

    test('Building provider state management', () async {
      final container = ProviderContainer();
      final notifier = container.read(buildingProvider.notifier);
      
      // Test initial state
      expect(container.read(buildingProvider).selectedBuildingNumber, isNull);
      expect(container.read(buildingProvider).currentRoomNumber, isNull);
      
      // Test setting building
      await notifier.setSelectedBuilding('123');
      expect(container.read(buildingProvider).selectedBuildingNumber, equals('123'));
      
      // Test setting room
      await notifier.setCurrentRoom('456');
      expect(container.read(buildingProvider).currentRoomNumber, equals('456'));
      
      // Test clearing room
      await notifier.clearCurrentRoom();
      expect(container.read(buildingProvider).currentRoomNumber, isNull);
      expect(container.read(buildingProvider).selectedBuildingNumber, equals('123'));
      
      // Test clearing all
      await notifier.clearBuildingState();
      expect(container.read(buildingProvider).selectedBuildingNumber, isNull);
      expect(container.read(buildingProvider).currentRoomNumber, isNull);
      
      container.dispose();
    });

    test('Reading provider functionality', () async {
      final container = ProviderContainer();
      final readingNotifier = container.read(readingProvider.notifier);
      
      // Test initial state
      expect(container.read(readingProvider).readings, isEmpty);
      expect(container.read(readingProvider).isLoading, isFalse);
      
      container.dispose();
    });

    test('Building number validation', () {
      final container = ProviderContainer();
      final notifier = container.read(buildingProvider.notifier);
      
      // Test valid building numbers
      expect(notifier.isValidBuildingNumber('1'), isTrue);
      expect(notifier.isValidBuildingNumber('123'), isTrue);
      expect(notifier.isValidBuildingNumber('9999'), isTrue);
      
      // Test invalid building numbers
      expect(notifier.isValidBuildingNumber(''), isFalse);
      expect(notifier.isValidBuildingNumber('0'), isFalse);
      expect(notifier.isValidBuildingNumber('10000'), isFalse);
      expect(notifier.isValidBuildingNumber('abc'), isFalse);
      expect(notifier.isValidBuildingNumber('-1'), isFalse);
      
      container.dispose();
    });

    test('Room number validation', () {
      final container = ProviderContainer();
      final notifier = container.read(buildingProvider.notifier);
      
      // Test valid room numbers
      expect(notifier.isValidRoomNumber('1'), isTrue);
      expect(notifier.isValidRoomNumber('123'), isTrue);
      expect(notifier.isValidRoomNumber('9999'), isTrue);
      
      // Test invalid room numbers
      expect(notifier.isValidRoomNumber(''), isFalse);
      expect(notifier.isValidRoomNumber('0'), isFalse);
      expect(notifier.isValidRoomNumber('10000'), isFalse);
      expect(notifier.isValidRoomNumber('abc'), isFalse);
      expect(notifier.isValidRoomNumber('-1'), isFalse);
      
      container.dispose();
    });
  });
}

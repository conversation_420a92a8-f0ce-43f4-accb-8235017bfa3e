import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:meter_pj/screens/building_selection_screen.dart';
import 'package:meter_pj/screens/room_entry_screen.dart';
import 'package:meter_pj/screens/meter_reading_screen.dart';
import 'package:meter_pj/screens/camera_screen.dart';
import 'package:meter_pj/models/meter_type.dart';
import 'package:meter_pj/providers/building_provider.dart';

void main() {
  group('Scroll Functionality Tests', () {
    testWidgets('Building selection screen has scroll view', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: BuildingSelectionScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Verify SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify building selection elements are present
      expect(find.text('Select Building'), findsOneWidget);
      expect(find.text('Building Number'), findsOneWidget);
    });

    testWidgets('Room entry screen has scroll view', (WidgetTester tester) async {
      // Create a container with building selected
      final container = ProviderContainer();
      await container.read(buildingProvider.notifier).setSelectedBuilding('123');
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: const MaterialApp(
            home: RoomEntryScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Verify SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify room entry elements are present
      expect(find.text('Enter Room'), findsOneWidget);
      expect(find.text('Building 123'), findsOneWidget);
      
      container.dispose();
    });

    testWidgets('Meter reading screen has scroll view', (WidgetTester tester) async {
      // Create a container with building and room selected
      final container = ProviderContainer();
      await container.read(buildingProvider.notifier).setSelectedBuilding('123');
      await container.read(buildingProvider.notifier).setCurrentRoom('456');
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: const MaterialApp(
            home: MeterReadingScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Verify SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify meter reading elements are present
      expect(find.text('Meter Reading'), findsOneWidget);
      expect(find.text('Building 123, Room 456'), findsOneWidget);
      
      container.dispose();
    });

    testWidgets('Camera screen has scroll view', (WidgetTester tester) async {
      // Create a container with building and room selected
      final container = ProviderContainer();
      await container.read(buildingProvider.notifier).setSelectedBuilding('123');
      await container.read(buildingProvider.notifier).setCurrentRoom('456');
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: CameraScreen(meterType: MeterType.main),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Verify SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify camera screen elements are present
      expect(find.text('Main Meter'), findsOneWidget);
      expect(find.text('Building 123, Room 456'), findsOneWidget);
      
      container.dispose();
    });

    testWidgets('Scroll views handle overflow correctly', (WidgetTester tester) async {
      // Test that scroll views can handle content that would overflow
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: BuildingSelectionScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find the scroll view
      final scrollView = find.byType(SingleChildScrollView);
      expect(scrollView, findsOneWidget);

      // Verify we can scroll (even if there's no overflow in this test)
      await tester.drag(scrollView, const Offset(0, -100));
      await tester.pumpAndSettle();

      // The screen should still be functional after scrolling
      expect(find.text('Select Building'), findsOneWidget);
    });

    test('Fixed height containers prevent overflow', () {
      // Test that our fixed height approach prevents overflow issues
      const fixedHeight = 200.0;
      const itemHeight = 60.0;
      const maxVisibleItems = fixedHeight / itemHeight;
      
      // With our fixed height of 200, we can show about 3-4 items comfortably
      expect(maxVisibleItems, greaterThan(3));
      expect(maxVisibleItems, lessThan(5));
    });

    test('Grid view dimensions are reasonable', () {
      // Test that our meter type grid has reasonable dimensions
      const gridHeight = 300.0;
      const crossAxisCount = 2;
      const childAspectRatio = 1.2;
      const spacing = 12.0;
      
      // Calculate approximate item height
      const availableWidth = 400.0; // Approximate screen width minus padding
      const itemWidth = (availableWidth - spacing) / crossAxisCount;
      const itemHeight = itemWidth / childAspectRatio;
      const rowHeight = itemHeight + spacing;
      const maxRows = gridHeight / rowHeight;
      
      // With 4 meter types and 2 columns, we need 2 rows
      const requiredRows = 4 / crossAxisCount;
      
      expect(maxRows, greaterThanOrEqualTo(requiredRows));
    });
  });
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:meter_pj/screens/export_screen.dart';
import 'package:meter_pj/screens/import_screen.dart';

import 'package:meter_pj/services/import_service.dart';
import 'package:meter_pj/models/meter_reading.dart';
import 'package:meter_pj/models/meter_type.dart';

void main() {
  group('Export/Import Tests', () {
    testWidgets('Export screen displays correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: ExportScreen())),
      );
      await tester.pump();

      // Verify basic export screen elements
      expect(find.text('Export Options'), findsOneWidget);
      expect(find.text('All Readings'), findsOneWidget);
      expect(find.text('By Building'), findsOneWidget);
      expect(find.text('By Date Range'), findsOneWidget);
    });

    testWidgets('Import screen displays correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: ImportScreen())),
      );
      await tester.pump();

      // Verify basic import screen elements
      expect(find.text('Select Import File'), findsOneWidget);
      expect(find.text('Select a ZIP file to import'), findsOneWidget);
      expect(find.text('Browse Files'), findsOneWidget);
    });

    test('Export service file size formatting', () {
      // Test file size formatting (accessing private method through reflection would be complex)
      // Instead, test the logic directly
      expect(_formatFileSize(500), equals('500 B'));
      expect(_formatFileSize(1536), equals('1.5 KB'));
      expect(_formatFileSize(1572864), equals('1.5 MB'));
      expect(_formatFileSize(**********), equals('1.5 GB'));
    });

    test('Import result validation', () {
      // Test successful import result
      final successResult = ImportResult(
        totalReadings: 10,
        importedReadings: 8,
        skippedReadings: 1,
        duplicateReadings: 1,
        errors: [],
      );

      expect(successResult.isSuccessful, isTrue);
      expect(successResult.hasErrors, isFalse);

      // Test failed import result
      final failedResult = ImportResult(
        totalReadings: 10,
        importedReadings: 5,
        skippedReadings: 2,
        duplicateReadings: 1,
        errors: ['Error 1', 'Error 2'],
      );

      expect(failedResult.isSuccessful, isFalse);
      expect(failedResult.hasErrors, isTrue);
      expect(failedResult.errors.length, equals(2));
    });

    test('Export data structure validation', () {
      final testReadings = [
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.main,
          readingValue: 1234.56,
          imagePath: '/test/image1.jpg',
          timestamp: DateTime(2024, 1, 1, 10, 30),
          notes: 'Test reading 1',
        ),
        MeterReading(
          buildingNumber: '123',
          roomNumber: '789',
          meterType: MeterType.water,
          readingValue: 987.65,
          imagePath: '/test/image2.jpg',
          timestamp: DateTime(2024, 1, 2, 14, 45),
          notes: 'Test reading 2',
        ),
      ];

      // Test data grouping
      final groupedByBuilding = <String, List<MeterReading>>{};
      for (final reading in testReadings) {
        groupedByBuilding
            .putIfAbsent(reading.buildingNumber, () => [])
            .add(reading);
      }

      expect(groupedByBuilding.keys.length, equals(1));
      expect(groupedByBuilding['123']?.length, equals(2));

      // Test metadata calculation
      final buildings = testReadings.map((r) => r.buildingNumber).toSet();
      final rooms =
          testReadings
              .map((r) => '${r.buildingNumber}-${r.roomNumber}')
              .toSet();
      final meterTypes =
          testReadings.map((r) => r.meterType.displayName).toSet();

      expect(buildings.length, equals(1));
      expect(rooms.length, equals(2));
      expect(meterTypes.length, equals(2));
      expect(meterTypes.contains('Main'), isTrue);
      expect(meterTypes.contains('Water'), isTrue);
    });

    test('Date range filtering for export', () {
      final testReadings = [
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.main,
          readingValue: 100.0,
          imagePath: '',
          timestamp: DateTime(2024, 1, 1),
        ),
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.water,
          readingValue: 200.0,
          imagePath: '',
          timestamp: DateTime(2024, 1, 15),
        ),
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.generator,
          readingValue: 300.0,
          imagePath: '',
          timestamp: DateTime(2024, 2, 1),
        ),
      ];

      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);

      // Filter readings by date range
      final filteredReadings =
          testReadings
              .where(
                (reading) =>
                    reading.timestamp.isAfter(
                      startDate.subtract(const Duration(days: 1)),
                    ) &&
                    reading.timestamp.isBefore(
                      endDate.add(const Duration(days: 1)),
                    ),
              )
              .toList();

      expect(filteredReadings.length, equals(2));
      expect(filteredReadings.every((r) => r.timestamp.month == 1), isTrue);
    });

    test('Building filtering for export', () {
      final testReadings = [
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.main,
          readingValue: 100.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
        MeterReading(
          buildingNumber: '456',
          roomNumber: '789',
          meterType: MeterType.water,
          readingValue: 200.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
        MeterReading(
          buildingNumber: '123',
          roomNumber: '101',
          meterType: MeterType.generator,
          readingValue: 300.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
      ];

      final targetBuilding = '123';

      // Filter readings by building
      final filteredReadings =
          testReadings
              .where((reading) => reading.buildingNumber == targetBuilding)
              .toList();

      expect(filteredReadings.length, equals(2));
      expect(filteredReadings.every((r) => r.buildingNumber == '123'), isTrue);
    });

    test('Import validation logic', () {
      // Test valid import data structure
      final validData = {
        'version': '1.0',
        'exportDate': DateTime.now().toIso8601String(),
        'totalReadings': 2,
        'readings': [
          {
            'buildingNumber': '123',
            'roomNumber': '456',
            'meterType': 'main',
            'readingValue': 100.0,
            'imagePath': '/test/image.jpg',
            'timestamp': DateTime.now().toIso8601String(),
            'notes': 'Test reading',
          },
        ],
      };

      expect(validData.containsKey('readings'), isTrue);
      expect(validData['readings'] is List, isTrue);
      expect((validData['readings'] as List).isNotEmpty, isTrue);

      // Test invalid data structure
      final invalidData = {
        'version': '1.0',
        'exportDate': DateTime.now().toIso8601String(),
        // Missing 'readings' key
      };

      expect(invalidData.containsKey('readings'), isFalse);
    });

    test('Duplicate detection logic', () {
      final existingReadings = [
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.main,
          readingValue: 100.0,
          imagePath: '',
          timestamp: DateTime(2024, 1, 1, 10, 0),
        ),
      ];

      final newReading = MeterReading(
        buildingNumber: '123',
        roomNumber: '456',
        meterType: MeterType.main,
        readingValue: 150.0,
        imagePath: '',
        timestamp: DateTime(2024, 1, 1, 10, 0),
      );

      // Test unique key generation for duplicate detection
      final existingKeys = existingReadings.map((r) => r.uniqueKey).toSet();
      final isDuplicate = existingKeys.contains(newReading.uniqueKey);

      expect(isDuplicate, isTrue);
    });

    test('Export filename generation', () {
      final now = DateTime.now();
      final timestamp = now.millisecondsSinceEpoch;

      // Test different export types
      final allReadingsFilename = 'meter_readings_all_readings_$timestamp.zip';
      final buildingFilename = 'meter_readings_building_123_$timestamp.zip';
      final dateRangeFilename =
          'meter_readings_readings_20240101_to_20240131_$timestamp.zip';

      expect(allReadingsFilename.contains('all_readings'), isTrue);
      expect(buildingFilename.contains('building_123'), isTrue);
      expect(dateRangeFilename.contains('20240101_to_20240131'), isTrue);
      expect(allReadingsFilename.endsWith('.zip'), isTrue);
    });
  });
}

// Helper function for testing file size formatting
String _formatFileSize(int bytes) {
  if (bytes < 1024) return '$bytes B';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
  if (bytes < 1024 * 1024 * 1024) {
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
  return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:meter_pj/services/share_service.dart';
import 'package:meter_pj/models/meter_reading.dart';
import 'package:meter_pj/models/meter_type.dart';
import 'package:meter_pj/screens/readings_list_screen.dart';

void main() {
  group('Share Functionality Tests', () {
    late ShareService shareService;

    setUp(() {
      shareService = ShareService();
    });

    test('ShareService can be instantiated', () {
      expect(shareService, isNotNull);
    });

    test('ShareService can check if sharing is available', () async {
      final canShare = await shareService.canShare();
      expect(canShare, isA<bool>());
    });

    test('ShareService can get available share targets', () async {
      final targets = await shareService.getAvailableShareTargets();
      expect(targets, isA<List<String>>());
      expect(targets.isNotEmpty, isTrue);
    });

    test('File size formatting works correctly', () {
      expect(_formatFileSize(500), equals('500 B'));
      expect(_formatFileSize(1536), equals('1.5 KB'));
      expect(_formatFileSize(1572864), equals('1.5 MB'));
      expect(_formatFileSize(1610612736), equals('1.5 GB'));
    });

    test('Share text generation for single file', () {
      final shareText = _generateShareText('test_export.zip', '1.2 MB');
      
      expect(shareText.contains('test_export.zip'), isTrue);
      expect(shareText.contains('1.2 MB'), isTrue);
      expect(shareText.contains('Meter Reading Export'), isTrue);
      expect(shareText.contains('Compatible with: Meter Reading App'), isTrue);
    });

    test('Share text generation for multiple files', () {
      final shareText = _generateMultipleShareText(3, '5.7 MB');
      
      expect(shareText.contains('3 export files'), isTrue);
      expect(shareText.contains('5.7 MB'), isTrue);
      expect(shareText.contains('Meter Reading Exports'), isTrue);
    });

    test('Export info text generation', () {
      final info = {
        'fileName': 'test_export.zip',
        'formattedSize': '2.1 MB',
        'createdDate': DateTime(2024, 1, 15, 10, 30),
        'readingsCount': 25,
        'buildingsCount': 3,
        'roomsCount': 8,
        'imagesCount': 15,
      };
      
      final infoText = _generateExportInfoText(info);
      
      expect(infoText.contains('test_export.zip'), isTrue);
      expect(infoText.contains('2.1 MB'), isTrue);
      expect(infoText.contains('Readings: 25'), isTrue);
      expect(infoText.contains('Buildings: 3'), isTrue);
      expect(infoText.contains('Rooms: 8'), isTrue);
      expect(infoText.contains('Images: 15'), isTrue);
    });

    test('CSV text generation from readings data', () {
      final readings = [
        {
          'buildingNumber': '123',
          'roomNumber': '456',
          'meterType': 'Main',
          'readingValue': 1234.56,
          'unit': 'kWh',
          'timestamp': '2024-01-15 10:30:00',
          'notes': 'Test reading',
        },
        {
          'buildingNumber': '789',
          'roomNumber': '101',
          'meterType': 'Water',
          'readingValue': 987.65,
          'unit': 'm³',
          'timestamp': '2024-01-16 14:45:00',
          'notes': 'Another test',
        },
      ];
      
      final csvText = _generateCSVText(readings);
      
      expect(csvText.contains('Building,Room,Meter Type'), isTrue);
      expect(csvText.contains('123,456,Main,1234.56'), isTrue);
      expect(csvText.contains('789,101,Water,987.65'), isTrue);
      expect(csvText.contains('"Test reading"'), isTrue);
      expect(csvText.contains('"Another test"'), isTrue);
    });

    test('CSV text handles empty readings list', () {
      final csvText = _generateCSVText([]);
      expect(csvText, equals('No readings data available'));
    });

    test('CSV text escapes commas in notes', () {
      final readings = [
        {
          'buildingNumber': '123',
          'roomNumber': '456',
          'meterType': 'Main',
          'readingValue': 100.0,
          'unit': 'kWh',
          'timestamp': '2024-01-15 10:30:00',
          'notes': 'Reading taken, verified, and confirmed',
        },
      ];
      
      final csvText = _generateCSVText(readings);
      
      // Commas in notes should be escaped with semicolons
      expect(csvText.contains('verified; and confirmed'), isTrue);
    });

    testWidgets('Readings list screen has share option', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: ReadingsListScreen(),
          ),
        ),
      );
      await tester.pump();

      // Find the popup menu button
      final popupMenuFinder = find.byType(PopupMenuButton<String>);
      expect(popupMenuFinder, findsOneWidget);

      // Tap the popup menu to open it
      await tester.tap(popupMenuFinder);
      await tester.pumpAndSettle();

      // Verify share option is present
      expect(find.text('Share Readings'), findsOneWidget);
    });

    test('Reading data conversion for sharing', () {
      final reading = MeterReading(
        buildingNumber: '123',
        roomNumber: '456',
        meterType: MeterType.main,
        readingValue: 1234.56,
        imagePath: '/test/image.jpg',
        timestamp: DateTime(2024, 1, 15, 10, 30),
        notes: 'Test reading',
      );

      final readingData = {
        'buildingNumber': reading.buildingNumber,
        'roomNumber': reading.roomNumber,
        'meterType': reading.meterType.displayName,
        'readingValue': reading.readingValue,
        'unit': reading.meterType.unit,
        'timestamp': '15/1/2024 10:30',
        'notes': reading.notes ?? '',
      };

      expect(readingData['buildingNumber'], equals('123'));
      expect(readingData['roomNumber'], equals('456'));
      expect(readingData['meterType'], equals('Main'));
      expect(readingData['readingValue'], equals(1234.56));
      expect(readingData['unit'], equals('kWh'));
      expect(readingData['notes'], equals('Test reading'));
    });

    test('Share title generation for filtered readings', () {
      const filteredCount = 15;
      const totalCount = 50;
      
      final filteredTitle = 'Filtered Meter Readings ($filteredCount readings)';
      final allTitle = 'All Meter Readings ($totalCount readings)';
      
      expect(filteredTitle.contains('Filtered'), isTrue);
      expect(filteredTitle.contains('15 readings'), isTrue);
      expect(allTitle.contains('All'), isTrue);
      expect(allTitle.contains('50 readings'), isTrue);
    });

    test('Share functionality handles empty readings list', () {
      final readings = <Map<String, dynamic>>[];
      final csvText = _generateCSVText(readings);
      
      expect(csvText, equals('No readings data available'));
    });

    test('Share service handles different meter types', () {
      final readings = [
        {
          'buildingNumber': '123',
          'roomNumber': '456',
          'meterType': 'Main',
          'readingValue': 100.0,
          'unit': 'kWh',
          'timestamp': '2024-01-15 10:30:00',
          'notes': '',
        },
        {
          'buildingNumber': '123',
          'roomNumber': '457',
          'meterType': 'Water',
          'readingValue': 50.0,
          'unit': 'm³',
          'timestamp': '2024-01-15 10:35:00',
          'notes': '',
        },
        {
          'buildingNumber': '123',
          'roomNumber': '458',
          'meterType': 'Generator',
          'readingValue': 75.0,
          'unit': 'kWh',
          'timestamp': '2024-01-15 10:40:00',
          'notes': '',
        },
      ];
      
      final csvText = _generateCSVText(readings);
      
      expect(csvText.contains('Main'), isTrue);
      expect(csvText.contains('Water'), isTrue);
      expect(csvText.contains('Generator'), isTrue);
      expect(csvText.contains('kWh'), isTrue);
      expect(csvText.contains('m³'), isTrue);
    });
  });
}

// Helper functions for testing (copied from ShareService)
String _formatFileSize(int bytes) {
  if (bytes < 1024) return '$bytes B';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
  if (bytes < 1024 * 1024 * 1024) {
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
  return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
}

String _generateShareText(String fileName, String fileSize) {
  return '''
📊 Meter Reading Export

📁 File: $fileName
📏 Size: $fileSize
📅 Shared: ${DateTime.now().toString().split('.')[0]}

This file contains meter reading data exported from the Meter Reading App. 
Import this file using the app's import function to restore the data.

🔧 Compatible with: Meter Reading App v1.0+
''';
}

String _generateMultipleShareText(int fileCount, String totalSize) {
  return '''
📊 Meter Reading Exports

📁 Files: $fileCount export files
📏 Total Size: $totalSize
📅 Shared: ${DateTime.now().toString().split('.')[0]}

These files contain meter reading data exported from the Meter Reading App. 
Import these files using the app's import function to restore the data.

🔧 Compatible with: Meter Reading App v1.0+
''';
}

String _generateExportInfoText(Map<String, dynamic> info) {
  final buffer = StringBuffer();
  buffer.writeln('📊 Meter Reading Export Information');
  buffer.writeln('');
  buffer.writeln('📁 File: ${info['fileName'] ?? 'Unknown'}');
  buffer.writeln('📏 Size: ${info['formattedSize'] ?? 'Unknown'}');
  buffer.writeln('📅 Created: ${info['createdDate'] ?? 'Unknown'}');
  buffer.writeln('');
  
  if (info.containsKey('readingsCount')) {
    buffer.writeln('📊 Data Summary:');
    buffer.writeln('• Readings: ${info['readingsCount']}');
    if (info.containsKey('buildingsCount')) {
      buffer.writeln('• Buildings: ${info['buildingsCount']}');
    }
    if (info.containsKey('roomsCount')) {
      buffer.writeln('• Rooms: ${info['roomsCount']}');
    }
    if (info.containsKey('imagesCount')) {
      buffer.writeln('• Images: ${info['imagesCount']}');
    }
  }
  
  buffer.writeln('');
  buffer.writeln('🔧 Compatible with: Meter Reading App v1.0+');
  
  return buffer.toString();
}

String _generateCSVText(List<Map<String, dynamic>> readings) {
  if (readings.isEmpty) {
    return 'No readings data available';
  }

  final buffer = StringBuffer();
  
  // CSV Header
  buffer.writeln('Building,Room,Meter Type,Reading Value,Unit,Timestamp,Notes');
  
  // CSV Data
  for (final reading in readings) {
    final building = reading['buildingNumber'] ?? '';
    final room = reading['roomNumber'] ?? '';
    final meterType = reading['meterType'] ?? '';
    final value = reading['readingValue'] ?? '';
    final unit = reading['unit'] ?? '';
    final timestamp = reading['timestamp'] ?? '';
    final notes = (reading['notes'] ?? '').replaceAll(',', ';'); // Escape commas
    
    buffer.writeln('$building,$room,$meterType,$value,$unit,$timestamp,"$notes"');
  }
  
  return buffer.toString();
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:meter_pj/screens/readings_list_screen.dart';
import 'package:meter_pj/screens/reading_detail_screen.dart';
import 'package:meter_pj/models/meter_reading.dart';
import 'package:meter_pj/models/meter_type.dart';


void main() {
  group('View Readings Tests', () {
    testWidgets('Readings list screen displays correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: ReadingsListScreen())),
      );
      await tester.pumpAndSettle();

      // Verify readings list screen elements
      expect(find.text('Meter Readings'), findsOneWidget);
      expect(find.byIcon(Icons.filter_list), findsOneWidget);
      expect(find.byIcon(Icons.sort), findsOneWidget);
      expect(find.text('Search readings...'), findsOneWidget);
    });

    testWidgets('Empty readings list shows correct message', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: ReadingsListScreen())),
      );
      await tester.pumpAndSettle();

      // Should show empty state
      expect(find.text('No readings recorded yet'), findsOneWidget);
      expect(find.text('Start by taking some meter readings'), findsOneWidget);
    });

    testWidgets('Reading detail screen displays correctly', (
      WidgetTester tester,
    ) async {
      final testReading = MeterReading(
        buildingNumber: '123',
        roomNumber: '456',
        meterType: MeterType.main,
        readingValue: 1234.56,
        imagePath: '/test/path.jpg',
        timestamp: DateTime.now(),
        notes: 'Test reading',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(home: ReadingDetailScreen(reading: testReading)),
        ),
      );
      await tester.pumpAndSettle();

      // Verify reading detail elements
      expect(find.text('Main Meter'), findsOneWidget);
      expect(find.textContaining('123'), findsWidgets);
      expect(find.textContaining('456'), findsWidgets);
      expect(find.text('1234.56'), findsOneWidget);
      expect(find.text('Test reading'), findsOneWidget);
    });

    test('Reading filtering works correctly', () {
      final readings = [
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.main,
          readingValue: 100.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
        MeterReading(
          buildingNumber: '789',
          roomNumber: '101',
          meterType: MeterType.water,
          readingValue: 200.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
      ];

      // Test building filter
      final buildingFiltered =
          readings.where((r) => r.buildingNumber == '123').toList();
      expect(buildingFiltered.length, equals(1));
      expect(buildingFiltered.first.buildingNumber, equals('123'));

      // Test meter type filter
      final typeFiltered =
          readings.where((r) => r.meterType == MeterType.water).toList();
      expect(typeFiltered.length, equals(1));
      expect(typeFiltered.first.meterType, equals(MeterType.water));
    });

    test('Reading sorting works correctly', () {
      final now = DateTime.now();
      final readings = [
        MeterReading(
          buildingNumber: '789',
          roomNumber: '456',
          meterType: MeterType.main,
          readingValue: 100.0,
          imagePath: '',
          timestamp: now.subtract(const Duration(hours: 1)),
        ),
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.water,
          readingValue: 200.0,
          imagePath: '',
          timestamp: now,
        ),
      ];

      // Test building sort
      readings.sort((a, b) => a.buildingNumber.compareTo(b.buildingNumber));
      expect(readings.first.buildingNumber, equals('123'));
      expect(readings.last.buildingNumber, equals('789'));

      // Test timestamp sort
      readings.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      expect(
        readings.first.timestamp.isBefore(readings.last.timestamp),
        isTrue,
      );
    });

    test('Reading search functionality', () {
      final readings = [
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.main,
          readingValue: 100.0,
          imagePath: '',
          timestamp: DateTime.now(),
          notes: 'Main meter reading',
        ),
        MeterReading(
          buildingNumber: '789',
          roomNumber: '101',
          meterType: MeterType.water,
          readingValue: 200.0,
          imagePath: '',
          timestamp: DateTime.now(),
          notes: 'Water meter reading',
        ),
      ];

      // Test search by building number
      final buildingSearch =
          readings
              .where((r) => r.buildingNumber.toLowerCase().contains('123'))
              .toList();
      expect(buildingSearch.length, equals(1));

      // Test search by meter type
      final typeSearch =
          readings
              .where(
                (r) => r.meterType.displayName.toLowerCase().contains('water'),
              )
              .toList();
      expect(typeSearch.length, equals(1));

      // Test search by notes
      final notesSearch =
          readings
              .where((r) => r.notes?.toLowerCase().contains('main') ?? false)
              .toList();
      expect(notesSearch.length, equals(1));
    });

    test('Reading statistics calculation', () {
      final readings = [
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.main,
          readingValue: 100.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
        MeterReading(
          buildingNumber: '123',
          roomNumber: '789',
          meterType: MeterType.water,
          readingValue: 200.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
        MeterReading(
          buildingNumber: '456',
          roomNumber: '101',
          meterType: MeterType.main,
          readingValue: 300.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
      ];

      // Calculate statistics
      final totalReadings = readings.length;
      final buildings = readings.map((r) => r.buildingNumber).toSet();
      final rooms =
          readings.map((r) => '${r.buildingNumber}-${r.roomNumber}').toSet();

      expect(totalReadings, equals(3));
      expect(buildings.length, equals(2)); // Buildings 123 and 456
      expect(rooms.length, equals(3)); // Three unique room combinations
    });

    test('Reading grouping functionality', () {
      final readings = [
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.main,
          readingValue: 100.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
        MeterReading(
          buildingNumber: '123',
          roomNumber: '456',
          meterType: MeterType.water,
          readingValue: 200.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
        MeterReading(
          buildingNumber: '789',
          roomNumber: '101',
          meterType: MeterType.main,
          readingValue: 300.0,
          imagePath: '',
          timestamp: DateTime.now(),
        ),
      ];

      // Group by building
      final groupedByBuilding = <String, List<MeterReading>>{};
      for (final reading in readings) {
        groupedByBuilding
            .putIfAbsent(reading.buildingNumber, () => [])
            .add(reading);
      }

      expect(groupedByBuilding.keys.length, equals(2));
      expect(groupedByBuilding['123']?.length, equals(2));
      expect(groupedByBuilding['789']?.length, equals(1));

      // Group by room
      final groupedByRoom = <String, List<MeterReading>>{};
      for (final reading in readings) {
        final roomKey = '${reading.buildingNumber}-${reading.roomNumber}';
        groupedByRoom.putIfAbsent(roomKey, () => []).add(reading);
      }

      expect(groupedByRoom.keys.length, equals(2));
      expect(groupedByRoom['123-456']?.length, equals(2));
      expect(groupedByRoom['789-101']?.length, equals(1));
    });
  });
}
